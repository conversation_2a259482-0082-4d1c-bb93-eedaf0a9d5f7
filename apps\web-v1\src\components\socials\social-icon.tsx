'use client'

import { CmsMedia } from '@ttplatform/core-page-builder/components'
import { TSocialSchema } from '@ttplatform/core-page-builder/libs'
import Image from 'next/image'

type TProps = {
  item: TSocialSchema
  className?: string
}

const SocialIcon = ({ item, className }: TProps) => {
  if (item.name === 'custom') {
    return <CmsMedia media={item.custom_icon} className={className} />
  }

  const iconMap = {
    facebook: '/icons/socials/ic-facebook.png',
    instagram: '/icons/socials/ic-instagram.png',
    linkedin: '/icons/socials/ic-linkedin.png',
    youtube: '/icons/socials/ic-youtube.png',
    tiktok: '/icons/socials/ic-tik-tok.png',
    email: '/icons/socials/ic-gmail.png',
    phone: '/icons/socials/ic-call.png',
  }

  const src = iconMap[item.name]

  if (!src) {
    return null
  }

  return (
    <Image
      src={src}
      alt={item.name}
      width={24}
      height={24}
      className={className}
    />
  )
}

export default SocialIcon
