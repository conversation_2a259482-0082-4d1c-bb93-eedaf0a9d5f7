import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '@ttplatform/ui/components'
import Autoplay from 'embla-carousel-autoplay'
import Image from 'next/image'
import { useRef } from 'react'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'
//---------------------------------------------------------------------------------
type TProps = {
  images: any
}
//---------------------------------------------------------------------------------
export default function GalleryImages({ images }: TProps) {
  const plugin = useRef(Autoplay({ delay: 5000, stopOnInteraction: true }))

  return (
    <Carousel
      plugins={[plugin.current]}
      opts={{
        align: 'start',
        loop: true,
      }}
      className="w-full relative"
    >
      <CarouselContent>
        {images?.map((item: any, idx: number) => (
          <CarouselItem key={idx} className="pl-4 md:pl-6 basis-3/4">
            <Image
              src={RenderImageUrlStrapi({ url: item?.url })}
              alt={item}
              width={1000}
              height={1000}
              className="w-full h-auto aspect-[9/6] object-cover rounded-2xl"
            />
          </CarouselItem>
        ))}
      </CarouselContent>
    </Carousel>
  )
}
