import { Skeleton } from '@ttplatform/ui/components'
import React, { Suspense } from 'react'

import { Sidebar } from '~/src/components/layouts'

interface LayoutProps {
  children: React.ReactNode
}

export default async function Layout({ children }: LayoutProps) {
  return (
    <Sidebar>
      <Suspense fallback={<Skeleton className="h-full w-full" />}>
        {children}
      </Suspense>
    </Sidebar>
  )
}
