import { BStaffMembers } from '@ttplatform/core-page-builder/components'
import { getStrapiImageUrl } from '@ttplatform/core-page-builder/libs'
import { LINGUI_CONFIG } from '~/src/config-global'
import StylesSection from '../../renderer/styles-section'

interface StaffMemberSectionProps {
  heading?: any
  items?: any[]
  styles?: any
  locale?: string
}

export default function StaffMemberSection({
  heading,
  items = [],
  styles,
  locale = LINGUI_CONFIG.defaultLocale,
}: StaffMemberSectionProps) {
  const mappedMembers = items.map((item) => ({
    name: item.name,
    position: item.position,
    image: getStrapiImageUrl(item.image, 'medium'),
  }))

  return (
    <StylesSection styles={styles}>
      <BStaffMembers
        members={mappedMembers}
        heading={heading}
        locale={locale}
        styles={styles}
      />
    </StylesSection>
  )
}
