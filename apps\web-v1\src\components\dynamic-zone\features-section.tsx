import { CmsMedia } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import Link from 'next/link'
import { cn } from '~/src/utils'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------
type TProps = {
  styles: any
  heading: any
  image: any
  features: any
}
//----------------------------------------------------------------------------------
export default function FeaturesSection({ image, features, styles }: TProps) {
  const {
    items,
    icon_position,
    text_align,
    item_border_radius,
    desktop_item_count,
    tablet_item_count,
    mobile_item_count,
    gap,
    item_background,
  } = features || {}

  const renderContent = (
    <div className="grid grid-cols-1 lg:grid-cols-12 gap-10">
      <div className="col-span-1 lg:col-span-2 flex justify-center">
        <CmsMedia
          media={image}
          className="w-full h-auto max-w-[150px] sm:max-w-[200px] lg:max-w-none"
        />
      </div>
      <div
        className={cn(
          'col-span-1 lg:col-span-10 grid',
          `grid-cols-${mobile_item_count} md:grid-cols-${tablet_item_count} lg:grid-cols-${desktop_item_count}`,
        )}
        style={{
          gap: gap || '0px',
        }}
      >
        {items?.map((item: any, idx: number) => (
          <IconBoxItem
            key={idx}
            item={item}
            icon_position={icon_position}
            text_align={text_align}
            item_background={item_background}
            item_border_radius={item_border_radius}
          />
        ))}
      </div>
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}

//----------------------------------------------------------------------------------
function IconBoxItem({
  item,
  icon_position,
  text_align,
  item_background,
  item_border_radius,
}: {
  item: any
  icon_position: any
  text_align: any
  item_background: any
  item_border_radius: any
}) {
  const renderIconPosition = (text: string) => {
    switch (text) {
      case 'top':
        return 'flex-col items-start'
      case 'center':
        return 'flex-col items-center'
      case 'left':
        return 'flex-row items-start'
    }
  }

  const renderIcon = (
    <CmsMedia
      media={item?.icon}
      className="w-8 min-w-8 sm:w-10 sm:min-w-10 md:w-12 md:min-w-12 aspect-square"
    />
  )
  const renderItem = (
    <div
      className={cn('flex gap-4', renderIconPosition(icon_position))}
      style={{
        backgroundImage: item_background?.image?.url
          ? `url(${RenderImageUrlStrapi({ url: item_background?.image?.url })})`
          : 'unset',
        backgroundColor: item_background?.color
          ? `rgb(from ${item_background?.color} r g b / ${item_background?.opacity})`
          : 'transparent',

        backgroundPosition: 'center center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundBlendMode: 'multiply',
        position: 'relative',
        borderRadius: item_border_radius || '0px',

        boxShadow: item_background?.color
          ? '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)'
          : 'unset',
      }}
    >
      {renderIcon}
      <div className={cn('flex flex-col gap-2', `text-${text_align}`)}>
        <Typography variant={'body1'} className="text-gray-800 font-bold">
          {item?.title}
        </Typography>
        <Typography variant={'body2'} className="text-gray-700 font-normal">
          {item?.sub_title}
        </Typography>
      </div>
    </div>
  )
  return (
    <>
      {item?.link?.url ? (
        <Link href={item?.link?.url} target={item?.link?.target}>
          {renderItem}
        </Link>
      ) : (
        renderItem
      )}
    </>
  )
}
