import { Toaster } from '@ttplatform/ui/components'
import { SlugProvider } from '~/src/context/slug-context'
import { SiteProvider } from '~/src/context/use-site-context'

type TProps = {
  children: React.ReactNode
}

export default async function Layout({ children }: TProps) {
  return (
    <SiteProvider>
      <SlugProvider>
        {children}
        <Toaster />
      </SlugProvider>
    </SiteProvider>
  )
}
