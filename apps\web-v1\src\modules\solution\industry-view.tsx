'use client'

import { use<PERSON>ara<PERSON> } from 'next/navigation'
import { useCallback, useEffect, useState } from 'react'
import C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '~/src/app/(locale)/client-slug-handler'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentTypeClient from '~/src/libs/cms/strapi/fetchContentTypeClient'
import { apiRoute } from '~/src/libs/constants/cms'
import { useLocale } from '~/src/libs/data/use-locale'
import { PageBreadcrumb } from '~/src/modules/layout'

//----------------------------------------------------------------------------------
type TProps = {
  categoryName: string
}
//----------------------------------------------------------------------------------
export default function IndustryView({ categoryName }: TProps) {
  const { segments } = useParams()

  const locale = useLocale()

  const [industryData, setIndustryData] = useState<any>(null)

  const fetchIndustryData = useCallback(async () => {
    const data = await fetchContentTypeClient({
      contentType: apiRoute.industrialSolutions,
      params: {
        filters: {
          slug: segments?.[1],
          locale,
        },
      },
    })
    setIndustryData(data?.data?.[0])
  }, [locale, segments])

  useEffect(() => {
    fetchIndustryData()
  }, [fetchIndustryData])

  const localizedSlugs = industryData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = localization.segments?.[1]

      return acc
    },
    { [locale]: segments?.[1] },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />

      <PageBreadcrumb
        items={[
          { title: categoryName, href: '#' },
          { title: industryData?.name },
        ]}
      />

      <PageContent pageData={industryData} />
    </>
  )
}
