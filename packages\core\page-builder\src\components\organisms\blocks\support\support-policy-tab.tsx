import { Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import React from 'react'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '../../../molecules'

interface PolicyItem {
  id: string
  title: string
  content: string
}

interface SupportPolicyTabProps {
  policyList: PolicyItem[]
}

export const SupportPolicyTab: React.FC<SupportPolicyTabProps> = ({
  policyList,
}) => (
  <div className="bg-white rounded-lg shadow p-4">
    <Accordion type="single" collapsible className="flex flex-col gap-3">
      {policyList.map((policy) => (
        <AccordionItem
          key={policy.id}
          value={policy.id}
          className="rounded-lg border-none bg-white last:mb-0"
        >
          <AccordionTrigger className="px-5 pl-6 py-5 data-[state=open]:pb-3 hover:no-underline cursor-pointer text-black">
            <Typography variant="body2" className="font-semibold">
              {policy.title}
            </Typography>
          </AccordionTrigger>
          <AccordionContent className={cn('px-5 pl-6 pb-4 text-gray-600')}>
            <Typography
              variant="body2"
              className="font-normal whitespace-pre-line"
            >
              {policy.content}
            </Typography>
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  </div>
)
