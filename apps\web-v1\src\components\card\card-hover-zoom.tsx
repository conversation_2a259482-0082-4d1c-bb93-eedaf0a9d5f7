import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'

import { MainButton } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import Image from 'next/image'
//----------------------------------------------------------------------------------

type IProps = {
  name: any
  description: any
  image: any
  href: string
}
//----------------------------------------------------------------------------------
export default function CardHoverZoom({
  name,
  description,
  image,
  href,
}: IProps) {
  return (
    <div className="flex flex-col md:flex-row items-center gap-x-10 group md:odd:flex-row-reverse">
      <div className="w-full md:w-1/2 flex flex-col gap-8">
        <Image
          src={'/icons/icon-hexagon.svg'}
          alt="category icon"
          width={50}
          height={40}
        />
        <div className="block md:hidden w-full h-auto aspect-[700/466] relative">
          <div
            className="w-full h-full absolute top-0 left-0 rounded-2xl group-hover:scale-105 md:group-hover:scale-110 ease-in-out transition-all duration-500"
            style={{
              backgroundImage: `url(${RenderImageUrlStrapi({ url: image?.url })})`,
              backgroundRepeat: 'no-repeat',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          ></div>
        </div>
        <div className="flex flex-col gap-2">
          <Typography
            variant={'h2'}
            className="relative pb-4 text-gray-800 uppercase before:absolute before:bottom-0  before:content-[''] before:w-[80px] before:h-[8px] before:bg-[#FFCC00] before:left-0"
          >
            {name}
          </Typography>
          <Typography variant={'body1'} className="text-gray-700 m-0">
            {description}
          </Typography>
        </div>

        <MainButton
          variant="secondary"
          isDisabledIcon={false}
          label={'Xem chi tiết'}
          url={href}
        />
      </div>
      <div className="hidden md:block md:w-1/2 h-auto aspect-[700/466] relative">
        <div
          className="w-full h-full absolute top-0 left-0 rounded-2xl group-hover:scale-110 ease-in-out transition-all duration-500"
          style={{
            backgroundImage: `url(${RenderImageUrlStrapi({ url: image?.url })})`,
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        ></div>
      </div>
    </div>
  )
}
