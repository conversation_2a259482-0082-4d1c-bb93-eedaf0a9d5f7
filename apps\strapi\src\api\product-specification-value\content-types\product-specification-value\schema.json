{"kind": "collectionType", "collectionName": "product_specification_values", "info": {"singularName": "product-specification-value", "pluralName": "product-specification-values", "displayName": "Product Specification-Value"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "value": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "value_in_us": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "product_specification": {"type": "relation", "relation": "manyToOne", "target": "api::product-specification.product-specification", "inversedBy": "product_specification_values"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "product_specification_values"}, "product_model": {"type": "relation", "relation": "manyToOne", "target": "api::product-model.product-model", "inversedBy": "product_specification_values"}}}