'use client'

import React from 'react'
import { LINGUI_CONFIG } from '~/src/config-global'
import { TestimonialItems } from '.'

interface TestimonialsSectionProps {
  heading?: any
  locale?: string
  styles?: any
  items?: any[]
}

const TestimonialsSection: React.FC<TestimonialsSectionProps> = ({
  heading,
  locale = LINGUI_CONFIG.defaultLocale,

  styles,
  items,
}) => {
  return (
    <TestimonialItems
      items={items}
      heading={heading}
      locale={locale}
      styles={styles}
    />
  )
}

export default TestimonialsSection
