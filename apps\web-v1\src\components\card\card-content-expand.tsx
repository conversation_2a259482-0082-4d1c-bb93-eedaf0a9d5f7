import { MainButton } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import Image from 'next/image'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'
//----------------------------------------------------------------------------------

type CardSolutionProps = {
  title: string
  description: string
  image: any
  href: string
}
//----------------------------------------------------------------------------------
export default function CardContentExpand({
  title,
  description,
  image,
  href,
}: CardSolutionProps) {
  return (
    <div className="relative w-full h-auto aspect-[6/9] flex items-end rounded-lg overflow-hidden group">
      <Image
        src={RenderImageUrlStrapi({ url: image?.url })}
        alt={title}
        fill
        width={0}
        height={0}
        sizes="100vw"
        priority
        className="rounded-xl transition-transform duration-400 group-hover:scale-105"
      />

      <div
        className="absolute inset-0 flex flex-col justify-end p-8 rounded-xl z-50"
        style={{}}
      >
        <Typography variant="h3" className="text-white font-bold ">
          {title}
          <span className="mt-1 md:mt-2 hidden group-hover:block w-[55px] md:w-[68px] h-[5px] md:h-[8px] rounded-none  bg-gradient-to-r from-[#FFCC00] via-[#FFD326] to-[#FFDE59]" />
        </Typography>
        <div className="sm:overflow-hidden">
          <div className="my-4 block sm:opacity-0 sm:h-0 sm:my-0 sm:group-hover:opacity-100 sm:group-hover:h-auto sm:group-hover:my-6 transition-all duration-300 text-white">
            <Typography variant="body1" className="line-clamp-3 text-white">
              {description}
            </Typography>
          </div>
          <div className="block sm:opacity-0 sm:h-0 sm:group-hover:opacity-100 sm:group-hover:h-auto transition-all duration-300">
            <MainButton label="XEM THÊM" variant="primary" url={href} />
          </div>
        </div>
      </div>
      <div
        className="absolute w-full h-[50%] bottom-0 left-0 z-20"
        style={{
          background:
            'linear-gradient(0deg, #222 0%, rgba(34, 34, 34, 0.00) 100%)',
        }}
      />
    </div>
  )
}
