'use client'

import { Icon } from '@iconify/react'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  Typography,
} from '@ttplatform/ui/components'
import Autoplay from 'embla-carousel-autoplay'
import { useRef } from 'react'
import { cn } from '~/src/utils'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'
import BlockEditor from '../renderer/block-editor'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
  items: any
}
//----------------------------------------------------------------------------------
export default function FeedbackSection({ styles, heading, items }: TProps) {
  const plugin = useRef(Autoplay({ delay: 5000, stopOnInteraction: true }))

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <div className="w-full">
        <Carousel
          // plugins={[plugin.current]}
          opts={{
            align: 'start',
            loop: true,
          }}
          onMouseEnter={plugin.current.stop}
          onMouseLeave={plugin.current.reset}
          className="w-full relative"
        >
          <CarouselContent
            className="ml-0 relative"
            style={{
              boxShadow:
                '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
            }}
          >
            {items?.map((item: any, idx: number) => {
              const renderImage = (
                <div
                  className="w-full h-full md:min-h-[520px]"
                  style={{
                    backgroundImage: `url(${RenderImageUrlStrapi({ url: item?.image?.url })})`,
                    backgroundPosition: 'center',
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: 'cover',
                  }}
                />
              )
              return (
                <CarouselItem key={idx} className="pl-0">
                  <div
                    className={cn(
                      'h-full grid grid-cols-1 md:grid-cols-2 justify-center items-center bg-white rounded-xl md:rounded-2xl overflow-hidden',
                    )}
                    style={{
                      boxShadow:
                        '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
                    }}
                  >
                    {/* image */}
                    <div className="hidden md:block h-full w-full">
                      {renderImage}
                    </div>
                    {/* text */}
                    <div className="h-full flex flex-col justify-between gap-5 md:gap-10 px-5 md:px-8 lg:px-10 pt-5 md:pt-10 pb-20 sm:pb-5 md:pb-10 min-h-500px] ">
                      <div className="flex flex-col gap-4 md:gap-6">
                        <div className="block md:hidden h-auto w-full aspect-[16/9] rounded-xl overflow-hidden">
                          {renderImage}
                        </div>
                        <div className="flex gap-1">
                          {Array.from({ length: 5 }).map((_, idx) => (
                            <Icon
                              key={idx}
                              icon="ic:baseline-star"
                              width="20"
                              height="20"
                              style={{
                                color: '#FDB022',
                              }}
                            />
                          ))}
                        </div>
                        <BlockEditor content={item?.text} />
                      </div>

                      <div className="w-full grid grid-cols-4 gap-2">
                        <div className="flex items-center gap-4 col-span-4 md:col-span-3 ">
                          <div
                            className="h-[50px] w-auto aspect-[56/51]"
                            style={{
                              clipPath:
                                'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                              backgroundImage: `url(${RenderImageUrlStrapi({ url: item?.user?.image?.url })})`,
                              backgroundPosition: 'center',
                              backgroundRepeat: 'no-repeat',
                              backgroundSize: 'cover',
                            }}
                          />
                          <div className="flex flex-col">
                            <Typography
                              variant={'body1'}
                              className="text-[101828]"
                            >
                              {item?.user?.name}
                            </Typography>
                            <Typography
                              variant={'body2'}
                              className="text-[#475467]"
                            >
                              {item?.user?.job}
                            </Typography>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              )
            })}
          </CarouselContent>
          <div className="absolute h-[50px] gap-2 z-50 right-[50%] translate-x-[50%] sm:translate-none sm:right-[20px] md:right-[32px] lg:right-[40px] bottom-[20px] md:bottom-[40px] flex items-center justify-between">
            <CarouselPrevious
              className="left-0 border-none bg-[#FFCC00] rounded-none h-8 md:h-10 w-auto aspect-[48/44] relative "
              style={{
                clipPath:
                  'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                top: 0,
                transform: 'translateY(50%)',
              }}
            />
            <CarouselNext
              className="right-0 border-none bg-[#FFCC00] rounded-none h-8 md:h-10 w-auto aspect-[48/44] relative shadow-lg"
              style={{
                clipPath:
                  'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                top: 0,
                transform: 'translateY(50%)',
              }}
            />
          </div>
        </Carousel>
      </div>
    </div>
  )

  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
