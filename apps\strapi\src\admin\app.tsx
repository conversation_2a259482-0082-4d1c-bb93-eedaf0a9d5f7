import type { StrapiApp } from '@strapi/strapi/admin'

// import PreviewButton from "./extensions/components/PreviewButton";

export default {
  config: {
    // head: {
    //   favicon: './favicon.ico',
    // },
    // menu: {
    //   logo: './logo.png',
    //   name: 'PTC Platform',
    // },
    // auth: {
    //   logo: './ptc.png',
    // },
    translations: {
      vi: {
        'Auth.form.welcome.title': 'Chào mừng đến với PTC Platform',
        'Auth.form.welcome.subtitle': 'Đăng nhập để tiếp tục',
      },
      en: {
        'Auth.form.welcome.title': 'Welcome to PTC Platform',
        'Auth.form.welcome.subtitle': 'Login to continue',
      },
    },
    locales: [
      // 'vi',
      // 'en'
    ],
    tutorials: false,
    notifications: {
      releases: false,
    },
    // Override or extend the theme
    theme: {
      dark: {
        colors: {
          // primary200: '#FFE8B3',
          // primary500: '#FFD966',
          // primary600: '#FFC61A',
          // primary700: '#FFB300',
          // primary800: '#FFA000',
          // primary900: '#FF8C00',
          // buttonPrimary500: '#FFD966',
          // buttonPrimary600: '#FFA000',
        },
      },
      light: {
        // you can see the light color here just like dark colors https://github.com/strapi/design-system/blob/main/packages/design-system/src/themes/lightTheme/light-colors.ts
      },
    },
  },
  bootstrap(_app: StrapiApp) {},
}
