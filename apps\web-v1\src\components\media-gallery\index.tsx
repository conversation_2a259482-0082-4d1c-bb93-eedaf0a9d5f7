import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON><PERSON>rigger,
  Typography,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { useState } from 'react'
import GalleryDocuments from './gallery-documents'
import GalleryImages from './gallery-images'
import GalleryVideos from './gallery-videos'
// import { HexagonIconButton } from '../../../../../../packages/core/page-builder/src/components/ui/custom-buttons/hexagon-button'
//---------------------------------------------------------------------------------
type TProps = {
  galleryData: any
  content_max_width: string
}
//---------------------------------------------------------------------------------
const _TABS_OPTIONS = [
  {
    label: 'HÌNH ẢNH',
    value: 'image',
  },
  {
    label: 'VIDEO',
    value: 'video',
  },
  {
    label: '360 VIEW',
    value: 'view',
  },
  {
    label: 'TÀI LIỆU',
    value: 'document',
  },
]

const _CONTENT_IMAGE = [
  '/images/products/related-1.png',
  '/images/products/related-2.png',
  '/images/products/related-3.png',
]
const _CONTENT_DOCUMENT = [
  'document-1.pdf',
  'document-2.pdf',
  'document-3.pdf',
  'document-4.pdf',
  'document-5.pdf',
]

const _VIEW_OPTIONS = [
  {
    label: 'View 360 từ bên ngoài',
    value: 'view-360-from-outside',
    image: '/images/products/may-cong-trinh.png',
  },
  {
    label: 'View 360 từ trong cabin',
    value: 'view-360-from-inside',
    image: '/images/products/view-360-inside.png',
  },
]

//---------------------------------------------------------------------------------
export default function MediaGallery({
  galleryData,
  content_max_width,
}: TProps) {
  const { images, gallery_videos, documents } = galleryData || {}

  const [tabSelect, setTabSelect] = useState('image')

  const [viewSelect, setViewSelect] = useState(_VIEW_OPTIONS?.[0])
  const handleChangeTab = (value: string) => {
    setTabSelect(value)
  }

  const renderContentView = (
    <div className="w-full grid grid-cols-12 gap-10">
      <div className="col-span-3 ">
        <div className="flex flex-col gap-4 p-6 rounded-2xl bg-white">
          <Typography variant={'h6'} className="text-gray-800 uppercase">
            view 360
          </Typography>
          <div className="flex flex-col">
            {_VIEW_OPTIONS.map((option) => (
              <div
                key={option?.value}
                className="w-full cursor-pointer hover:bg-gray-100 p-2 rounded-lg"
                onClick={() => setViewSelect(option)}
              >
                <Typography
                  variant={'body1'}
                  className={cn(
                    'text-gray-800',
                    viewSelect?.value === option?.value && 'font-semibold',
                  )}
                >
                  {option?.label}
                </Typography>
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className="col-span-9">
        <div
          className="w-full h-auto aspect-[4/3] object-cover rounded-2xl"
          style={{
            backgroundImage: `url(${viewSelect?.image})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
          }}
        ></div>
      </div>
    </div>
  )

  return (
    <div className="flex flex-col gap-10">
      <Tabs
        value={tabSelect}
        onValueChange={handleChangeTab}
        className="w-full max-w-screen-lg mx-auto"
      >
        <TabsList className="w-full h-auto grid grid-cols-4 bg-transparent mx-auto gap-2 sm:gap-4">
          {_TABS_OPTIONS.map((option) => (
            <TabsTrigger
              key={option?.value}
              value={option?.value}
              className="w-full py-2 px-3 text-white bg-[#313131] data-[state=active]:text-gray-800 data-[state=active]:bg-[#FFCC00]  border-b-2 border-b-transparent cursor-pointer rounded-full"
            >
              <Typography variant={'body2'}>{option?.label}</Typography>
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
      {/* content */}
      <div className="w-full max-h-[800px]">
        {tabSelect === 'image' && <GalleryImages images={images} />}
        {tabSelect === 'video' && (
          <GalleryVideos
            gallery_videos={gallery_videos}
            content_max_width={content_max_width}
          />
        )}
        {tabSelect === 'view' && renderContentView}
        {tabSelect === 'document' && <GalleryDocuments documents={documents} />}
      </div>
    </div>
  )
}
