msgid ""
msgstr ""
"POT-Creation-Date: 2025-06-24 13:06+0700\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: en\n"
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#. js-lingui-explicit-id
#: src/app/(locale)/(web)/(product-part-pages)/parts/page.tsx:53
msgid "Parts"
msgstr "Parts"

#. js-lingui-explicit-id
#: src/modules/layout/page-breadcrumb.tsx:35
msgid "Home"
msgstr "Home"

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:120
msgid "Search"
msgstr "Search"

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:122
msgid "Enter your search query below to find what you're looking for."
msgstr "Enter your search query below to find what you're looking for."

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:132
msgid "Search products, news, promotions, careers..."
msgstr "Search products, news, promotions, careers..."

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:150
msgid "Loading search data..."
msgstr "Loading search data..."

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:157
msgid "Failed to load search data"
msgstr "Failed to load search data"

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:232
msgid "No results found for \"{query}\""
msgstr "No results found for \"{query}\""

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:238
msgid "Try different keywords or check spelling"
msgstr "Try different keywords or check spelling"

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:249
msgid "Start typing to search across the website"
msgstr "Start typing to search across the website"

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:251
msgid "Products, news, promotions, careers, services and more..."
msgstr "Products, news, promotions, careers, services and more..."

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:259
msgid "Press"
msgstr "Press"

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:263
msgid "to open this dialog again."
msgstr "to open this dialog again."

#. js-lingui-explicit-id
#: src/app/(locale)/(web)/services/page.tsx:55
#: src/app/(locale)/(web)/services/[slug]/page.tsx:74
msgid "Services"
msgstr "Services"

#. js-lingui-explicit-id
#: src/app/(locale)/(web)/promotions/page.tsx:58
#: src/app/(locale)/(web)/promotions/[slug]/page.tsx:66
msgid "Promotions"
msgstr "Promotions"

#. js-lingui-explicit-id
#: src/app/(locale)/(web)/news/page.tsx:56
#: src/app/(locale)/(web)/news/[slug]/page.tsx:65
msgid "News"
msgstr "News"

#. js-lingui-explicit-id
#: src/app/(locale)/(web)/careers/page.tsx:56
#: src/app/(locale)/(web)/careers/[slug]/page.tsx:71
msgid "Careers"
msgstr "Careers"

#. js-lingui-explicit-id
#: src/app/(locale)/(web)/(product-pages)/products/[slug]/page.tsx:83
#: src/app/(locale)/(web)/(product-pages)/products/[slug]/page.tsx:133
msgid "Products"
msgstr "Products"

#. js-lingui-explicit-id
#: src/app/(locale)/(web)/(product-pages)/product-categories/[slug]/page.tsx:78
msgid "Product Categories"
msgstr "Product Categories"

#. placeholder {0}: item?.name
#. placeholder {0}: spec.name
#: src/modules/product/components/filter-results.tsx:204
#: src/modules/product/components/filter-bar.tsx:295
#: src/modules/product/components/filter-bar.tsx:318
msgid "{0}"
msgstr "{0}"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:123
msgid "Account created successfully!"
msgstr "Account created successfully!"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:120
msgid "account."
msgstr "account."

#: src/modules/product/components/filter-bar.tsx:220
msgid "All Products"
msgstr "All Products"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:295
msgid "Already have an account?"
msgstr "Already have an account?"

#: src/modules/product/components/filter-bar.tsx:195
#~ msgid "Bolt length (MM)"
#~ msgstr "Bolt length (MM)"

#: src/modules/product/components/filter-bar.tsx:216
#~ msgid "Bore Diameter (MM)"
#~ msgstr "Bore Diameter (MM)"

#: src/components/dynamic-zone/form/form-subscriber.tsx:97
msgid "Cảm ơn bạn đã đăng ký!"
msgstr "Cảm ơn bạn đã đăng ký!"

#: src/modules/product/components/filter-bar.tsx:186
msgid "Category"
msgstr "Category"

#: src/modules/contact/components/contact-form.tsx:103
#: src/components/dynamic-zone/form/form-subscriber.tsx:101
msgid "Có lỗi xảy ra. Vui lòng thử lại sau."
msgstr "Có lỗi xảy ra. Vui lòng thử lại sau."

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:233
msgid "Confirm password"
msgstr "Confirm password"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:231
msgid "Confirm Password"
msgstr "Confirm Password"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:24
msgid "Confirm password must be at least 8 characters"
msgstr "Confirm password must be at least 8 characters"

#: src/modules/contact/components/contact-form.tsx:138
#: src/modules/contact/components/contact-form.tsx:141
msgid "Công ty"
msgstr "Công ty"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:258
msgid "Create account"
msgstr "Create account"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:290
msgid "Create Account"
msgstr "Create Account"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:219
msgid "Create password"
msgstr "Create password"

#: src/modules/contact/components/contact-form.tsx:257
#: src/modules/contact/components/contact-form.tsx:261
msgid "Đang gửi..."
msgstr "Đang gửi..."

#: src/modules/product/components/filter-results.tsx:156
msgid "Display Mode:"
msgstr "Display Mode:"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:256
msgid "Don’t have an account?"
msgstr "Don’t have an account?"

#: src/modules/product/components/filter-bar.tsx:242
#~ msgid "Efficiency Rating"
#~ msgstr "Efficiency Rating"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:208
#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:134
msgid "Email"
msgstr "Email"

#: src/components/dynamic-zone/form/form-subscriber.tsx:43
msgid "Email is required"
msgstr "Email is required"

#: src/modules/contact/components/contact-form.tsx:27
msgid "Email không hợp lệ"
msgstr "Email không hợp lệ"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:209
#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:140
msgid "Enter your email"
msgstr "Enter your email"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:197
msgid "Enter your name"
msgstr "Enter your name"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:165
msgid "Enter your password"
msgstr "Enter your password"

#: src/modules/product/components/filter-bar.tsx:196
msgid "Error loading categories. Please try again."
msgstr "Error loading categories. Please try again."

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:176
msgid "Explore a range of features designed to elevate your business and drive growth."
msgstr "Explore a range of features designed to elevate your business and drive growth."

#: src/modules/product/components/filter-bar.tsx:388
msgid "FILTER PRODUCTS"
msgstr "FILTER PRODUCTS"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:206
msgid "Forgot password?"
msgstr "Forgot password?"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:196
msgid "Full Name"
msgstr "Full Name"

#: src/modules/contact/components/contact-form.tsx:257
#: src/modules/contact/components/contact-form.tsx:261
msgid "Gửi yêu cầu"
msgstr "Gửi yêu cầu"

#: src/modules/contact/components/contact-form.tsx:98
msgid "Gửi yêu cầu thành công! Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất."
msgstr "Gửi yêu cầu thành công! Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất."

#: src/modules/contact/components/contact-form.tsx:117
#: src/modules/contact/components/contact-form.tsx:129
msgid "Họ và tên"
msgstr "Họ và tên"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:260
msgid "I agree to the"
msgstr "I agree to the"

#: src/modules/product/components/filter-bar.tsx:268
#~ msgid "Inside Diameter (MM)"
#~ msgstr "Inside Diameter (MM)"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:66
msgid "Invalid credentials"
msgstr "Invalid credentials"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:56
msgid "Invalid credentials: email or password is invalid"
msgstr "Invalid credentials: email or password is invalid"

#: src/components/dynamic-zone/form/form-subscriber.tsx:44
#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:20
msgid "Invalid email format"
msgstr "Invalid email format"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:174
msgid "Join us today and unlock the full potential of"
msgstr "Join us today and unlock the full potential of"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:199
msgid "Keep me sign-in"
msgstr "Keep me sign-in"

#: src/modules/contact/components/contact-form.tsx:23
#: src/modules/contact/components/contact-form.tsx:26
#: src/modules/contact/components/contact-form.tsx:28
#: src/modules/contact/components/contact-form.tsx:30
msgid "Không được để trống"
msgstr "Không được để trống"

#: src/modules/product/components/filter-results.tsx:177
#: src/modules/product/components/filter-results.tsx:217
msgid "Loading products..."
msgstr "Loading products..."

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:222
msgid "Login now"
msgstr "Login now"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:60
msgid "Login Successful"
msgstr "Login Successful"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:118
msgid "Login with your email and password you have been created before, or you can create an account if you don’t have a"
msgstr "Login with your email and password you have been created before, or you can create an account if you don’t have a"

#: src/modules/product/b-generator-selection-guide.tsx:105
msgid "Module"
msgstr "Module"

#: src/components/dynamic-zone/form/form-subscriber.tsx:40
#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:19
msgid "Name is required"
msgstr "Name is required"

#: src/modules/contact/components/contact-form.tsx:245
msgid "Nhập nội dung yêu cầu"
msgstr "Please enter the content"

#: src/modules/product/components/filter-results.tsx:183
#: src/modules/product/components/filter-results.tsx:225
msgid "No products found for selected filters."
msgstr "No products found for selected filters."

#: src/modules/contact/components/contact-form.tsx:233
msgid "Nội dung yêu cầu"
msgstr "Required"

#: src/app/(locale)/(web)/(product-part-pages)/parts/page.tsx:53
#~ msgid "Parts"
#~ msgstr "Parts"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:217
#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:157
msgid "Password"
msgstr "Password"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:21
msgid "Password must be at least 8 characters"
msgstr "Password must be at least 8 characters"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:31
msgid "Passwords must match"
msgstr "Passwords must match"

#: src/modules/product/components/filter-results.tsx:168
msgid "PRODUCT MODELS"
msgstr "PRODUCT MODELS"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:300
msgid "Sign in"
msgstr "Sign in"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:128
msgid "Sign-up failed"
msgstr "Sign-up failed"

#: src/modules/contact/components/contact-form.tsx:154
msgid "Số điện thoại"
msgstr "Số điện thoại"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:130
msgid "Something went wrong"
msgstr "Something went wrong"

#: src/modules/product/components/filter-bar.tsx:408
msgid "Specifications"
msgstr "Specifications"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:262
msgid "Terms & Conditions"
msgstr "Terms & Conditions"

#: src/modules/product/b-product-categories.tsx:72
msgid "View Details"
msgstr "View Details"

#: src/components/card/card-solution-category.tsx:51
msgid "View More"
msgstr "View More"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:105
msgid "Welcome back to"
msgstr "Welcome back to"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:161
msgid "Welcome to"
msgstr "Welcome to"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:26
#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:272
msgid "You must agree to the terms and conditions"
msgstr "You must agree to the terms and conditions"
