import { Metadata } from 'next'
import { lazy } from 'react'

const SignUpForm = lazy(() => import('./_form'))

export const metadata: Metadata = {
  title: 'Sign Up | PTC Platform',
  description: 'Sign up to the app',
}

export default function SignUpPage() {
  return (
    <div
      className="mx-auto flex min-h-screen w-full flex-col items-center justify-center bg-[#000]"
      style={{
        background:
          'radial-gradient(64.517% 100% at 50% 100%, rgba(2, 156, 236, 0.2) 0%, rgba(245, 245, 245, 1) 100%)',
        bottom: 0,
        flex: '1',
        left: 0,
        position: 'absolute',
        right: 0,
        zIndex: 0,
      }}
    >
      <SignUpForm />
    </div>
  )
}
