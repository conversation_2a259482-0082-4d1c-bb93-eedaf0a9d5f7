// TODO: FIXME

import { CmsMedia } from '@ttplatform/core-page-builder/components'
import { TLinkSchema, TMediaSchema } from '@ttplatform/core-page-builder/libs'
import { AspectRatio } from '@ttplatform/ui/components'
import Link from 'next/link'
import { cn } from '~/src/utils'

type TProps = {
  image: TMediaSchema
  link: TLinkSchema
  ratio?: number
  className?: string
}

export const BannerAds = ({
  image,
  link,
  ratio = 16 / 9,
  className,
}: TProps) => {
  const renderImage = (
    <AspectRatio ratio={ratio}>
      <CmsMedia media={image} />
    </AspectRatio>
  )

  if (link) {
    return (
      <Link
        href={link.url}
        target={link.target}
        className={cn('w-full h-full', className)}
      >
        {renderImage}
      </Link>
    )
  }

  return <div className={cn('w-full h-full', className)}>{renderImage}</div>
}
