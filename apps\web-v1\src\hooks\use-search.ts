'use client'

import Fuse from 'fuse.js'
import { useEffect, useMemo, useState } from 'react'

export interface SearchItem {
  documentId: string
  name: string
  description: string
  type: string
  link: string
  thumbnail?: string | null
  priority: number // Add priority for search ordering
  tagline?: string
  category?: string
  tags?: string
}

export interface SearchResult {
  item: SearchItem
  score?: number
}

export function useSearch() {
  const [searchData, setSearchData] = useState<SearchItem[]>([])
  const [searchIndex, setSearchIndex] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load search data and index
  useEffect(() => {
    async function loadSearchAssets() {
      try {
        setIsLoading(true)

        // Load search data and index in parallel with cache busting
        const timestamp = Date.now()
        const [dataResponse, indexResponse] = await Promise.all([
          fetch(`/search-data.json?v=${timestamp}`),
          fetch(`/search-index.json?v=${timestamp}`),
        ])

        if (!dataResponse.ok || !indexResponse.ok) {
          throw new Error('Failed to load search assets')
        }

        const data = await dataResponse.json()
        const indexData = await indexResponse.json()

        setSearchData(data)
        setSearchIndex(Fuse.parseIndex(indexData))
        setError(null)
      } catch (err) {
        console.error('Error loading search assets:', err)
        setError('Failed to load search data')
      } finally {
        setIsLoading(false)
      }
    }

    loadSearchAssets()
  }, [])

  // Create Fuse instance
  const fuse = useMemo(() => {
    if (!searchData.length || !searchIndex) return null

    const options = {
      keys: [
        { name: 'name', weight: 0.4 },
        { name: 'description', weight: 0.3 },
        { name: 'tagline', weight: 0.2 },
        { name: 'category', weight: 0.1 },
        { name: 'tags', weight: 0.1 },
      ],
      threshold: 0.6,
      includeScore: true,
      includeMatches: false,
    }

    return new Fuse(searchData, options, searchIndex)
  }, [searchData, searchIndex])

  // Search function
  const search = useMemo(() => {
    return (query: string): SearchResult[] => {
      if (!fuse || !query.trim()) return []

      const lowerQuery = query.toLowerCase()

      const exactMatches = searchData.filter((item) => {
        const name = String(item.name || '').toLowerCase()
        const description = String(item.description || '').toLowerCase()
        const tagline = String(item.tagline || '').toLowerCase()
        const category = String(item.category || '').toLowerCase()
        const tags = String(item.tags || '').toLowerCase()

        return (
          name.includes(lowerQuery) ||
          description.includes(lowerQuery) ||
          tagline.includes(lowerQuery) ||
          category.includes(lowerQuery) ||
          tags.includes(lowerQuery)
        )
      })

      if (exactMatches.length > 0) {
        return exactMatches
          .sort((a, b) => a.priority - b.priority) // Sort by priority first
          .map((item) => ({
            item,
            score: 0.1 - item.priority * 0.01, // Lower score for higher priority
          }))
      }

      const fuseResults = fuse.search(query)
      return fuseResults
        .map((result) => ({
          item: result.item,
          score: (result.score || 0) + result.item.priority * 0.1, // Add priority penalty to score
        }))
        .sort((a, b) => (a.score || 0) - (b.score || 0)) // Sort by final score
    }
  }, [fuse, searchData])

  return {
    search,
    searchData,
    isLoading,
    error,
    isReady: !isLoading && !error && !!fuse,
  }
}
