import { LayoutContainer } from '@ttplatform/core-page-builder/components'
import {
  THeadingSchema,
  TPaginationSchema,
} from '@ttplatform/core-page-builder/libs'
import { useCallback, useState } from 'react'
import { useGetPromotions } from '~/src/libs/cms/strapi/use-promotions'
import { useLocale } from '~/src/libs/data/use-locale'
import NewsPromotionListing from '~/src/modules/news-promotions/news-promotion-listing'

type TProps = {
  heading: THeadingSchema
  limit: number
  pagination: TPaginationSchema
  banner_ads?: any
}

const PromotionListing = ({ heading, pagination, banner_ads }: TProps) => {
  const limit = pagination.limit || 12
  const [paginationLimit, setPaginationLimit] = useState<number>(limit)
  const [sort, setSort] = useState<string | string[]>('publishedAt:desc')
  const [filter, setFilter] = useState<Record<any, string>>()
  const locale = useLocale()
  const handleFilter = useCallback((name: string, value: any) => {
    setFilter((prev: any) => ({ ...prev, [name]: value }))
  }, [])

  const handleSort = useCallback((sort: string | string[]) => {
    setSort(sort)
  }, [])

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const handleLoadMore = useCallback(() => {
    setPaginationLimit((prev) => prev + limit)
  }, [limit])

  const { data, isLoading, error } = useGetPromotions({
    locale,
    pagination: {
      limit: paginationLimit,
      start: 0,
    },
    sort,
    filters: filter,
  })
  return (
    <LayoutContainer>
      <NewsPromotionListing
        heading={heading}
        banner_ads={banner_ads}
        data={data}
        paginationLimit={paginationLimit}
        isLoading={isLoading}
        error={error}
        handleLoadMore={handleLoadMore}
        sort={sort}
        handleSort={handleSort}
        handleFilter={handleFilter}
        filter={filter}
      />
    </LayoutContainer>
  )
}

export default PromotionListing
