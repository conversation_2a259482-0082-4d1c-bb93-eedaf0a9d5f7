// Product related types for Strapi API

export interface ProductCategory {
  id: number
  documentId: string
  name: string
  slug: string
  excerpt?: string
  content?: any[]
  rank?: number
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  image?: {
    id: number
    documentId: string
    name: string
    url: string
    alternativeText?: string
    width: number
    height: number
    formats?: any
  }
  products?: any[]
  parent?: ProductCategory
  children?: ProductCategory[]
  localizations?: any[]
}

export interface ProductSpecification {
  id: number
  documentId: string
  name: string
  unit?: string
  unit_in_us?: string
  rank: number
  is_root: boolean
  type: string
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  parent?: ProductSpecification
  children?: ProductSpecification[]
  product_specification_values?: ProductSpecificationValue[]
  localizations?: any[]
}

export interface ProductSpecificationValue {
  id: number
  documentId: string
  value: string
  value_in_us?: string
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  product_specification?: ProductSpecification
  product?: {
    id: number
    documentId: string
    name: string
    slug: string
    featured: boolean
  }
  product_model?: {
    id: number
    documentId: string
    name: string
    slug?: string
    rank?: number
  }
  localizations?: any[]
}

// API Response types
export interface StrapiResponse<T> {
  data: T[]
  meta?: {
    pagination?: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

export type ProductCategoriesResponse = StrapiResponse<ProductCategory>
export type ProductSpecificationsResponse = StrapiResponse<ProductSpecification>
export type ProductSpecificationValuesResponse =
  StrapiResponse<ProductSpecificationValue>

// Filter types for UI
export interface FilterState {
  categories: string[]
  boltLength: number[]
  boreDiameter: number[]
  efficiencyRating: string[]
  insideDiameter: number[]
}

export interface FilterOption {
  id: string | number
  label: string
  value: string | number
  usValue?: string | number // US unit value
  count?: number
}

// Dynamic filter specification types
export interface DynamicOptionsFilter {
  id: string
  name: string
  slug: string
  type: 'options'
  options: FilterOption[]
  isLoading: boolean
  unit?: string // Metric unit
  unit_in_us?: string // US unit
}

export type DynamicFilterSpec = DynamicOptionsFilter
