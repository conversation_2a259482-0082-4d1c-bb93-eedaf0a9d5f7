'use client'

import {
  Carousel,
  Carousel<PERSON>ontent,
  Carouse<PERSON><PERSON><PERSON>,
  Carousel<PERSON><PERSON><PERSON>,
  Carousel<PERSON>revious,
  Dialog,
  DialogContent,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { Play } from 'lucide-react'
import { useState } from 'react'
import {
  RenderImageUrlStrapi,
  RenderLinkUrlStrapi,
} from '../render-imageUrl-strapi'
//----------------------------------------------------------------------------------

type TProps = {
  gallery_videos: any
  content_max_width: string
}
//----------------------------------------------------------------------------------
export default function GalleryVideos({
  gallery_videos,
  content_max_width,
}: TProps) {
  const fullWidth = content_max_width == 'full_width'

  const [videoSelect, setVideoSelect] = useState(gallery_videos?.videos?.[0])

  const [openDialog, setOpenDialog] = useState(false)

  const renderVideoBox = (
    <video
      controls
      playsInline
      // autoPlay
      muted
      loop
      preload="metadata"
      poster={RenderImageUrlStrapi({
        url: videoSelect?.image_poster?.url,
      })}
      style={{
        height: 'auto',
        width: '100%',
        aspectRatio: '16/9',
        objectFit: 'cover',
        borderRadius: gallery_videos?.border_radius
          ? gallery_videos?.border_radius
          : 'unset',
      }}
    >
      <source
        rel="preload"
        src={
          videoSelect?.video?.url
            ? RenderLinkUrlStrapi({ url: videoSelect?.video?.url })
            : videoSelect?.embed
        }
        type={'video/mp4'}
      />
    </video>
  )

  if (!gallery_videos && !gallery_videos?.videos?.length) {
    return null
  }

  return (
    <>
      {videoSelect ? (
        <div className={cn('flex flex-col gap-5 md:gap-10 relative z-30')}>
          {gallery_videos?.open_type == 'playin' ? (
            <div className="w-full h-auto">{renderVideoBox}</div>
          ) : null}
          {gallery_videos?.open_type == 'dialog' ? (
            <div
              className="w-full h-auto aspect-[16/9] flex items-center justify-center group "
              style={{
                backgroundImage: `url(${RenderImageUrlStrapi({ url: videoSelect?.image_poster?.url })})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                backgroundBlendMode: 'overlay',
                backgroundColor: 'rgba(0, 0, 0, 0.3)',
                borderRadius: gallery_videos?.border_radius
                  ? gallery_videos?.border_radius
                  : 'unset',
              }}
            >
              <div
                onClick={() => setOpenDialog(true)}
                className="cursor-pointer flex px-3 py-2 md:px-5 md:py-3 bg-black/60 rounded-lg md:rounded-2xl group-hover:bg-black/80 transition-all duration-300"
              >
                <Play className="text-white w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8 " />
              </div>
            </div>
          ) : null}
          {gallery_videos?.videos?.length > 1 ? (
            <Carousel
              opts={{
                align: 'start',
              }}
              className={cn(
                'w-screen-md mx-auto px-10',
                fullWidth
                  ? 'absolute z-50 bottom-0 left-[50%] translate-x-[-50%] py-10'
                  : '',
              )}
            >
              <CarouselContent>
                {gallery_videos?.videos?.map((item: any, idx: number) => (
                  <CarouselItem
                    key={idx}
                    onClick={() => setVideoSelect(item)}
                    className={cn(
                      'basis-1/3 flex justify-center cursor-pointer',
                      gallery_videos?.videos?.length > 2
                        ? 'sm:basis-1/3'
                        : 'basis-1/2',
                    )}
                  >
                    <div
                      className={cn(
                        ' max-w-max',
                        videoSelect?.id === item?.id
                          ? 'bg-[#FFCC00]'
                          : 'bg-gray-200',
                      )}
                      style={{
                        clipPath:
                          'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                      }}
                    >
                      <div
                        className="w-[30px] sm:w-[50px] md:w-[70px] h-auto aspect-[100/89] bg-red-300"
                        style={{
                          clipPath:
                            'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                          backgroundImage: `url(${RenderImageUrlStrapi({ url: item?.image_poster?.url })})`,
                          backgroundSize: 'cover',
                          backgroundPosition: 'center',
                          backgroundRepeat: 'no-repeat',
                        }}
                      ></div>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              {gallery_videos?.videos?.length > 2 && (
                <CarouselPrevious
                  className=" left-0 bg-[#FC0] text-gray-800 hover:text-white border-transparent rounded-none hover:bg-[#FC0]"
                  style={{
                    clipPath:
                      'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                    boxShadow:
                      '0px 30px 40px 0px rgba(0, 0, 0, 0.5), 0px 0px 20px 0px rgba(0, 0, 0, 0.5), 0px -10px 10px 0px rgba(255, 255, 255, 0.2)',
                  }}
                />
              )}
              {gallery_videos?.videos?.length > 2 && (
                <CarouselNext
                  className="right-0 bg-[#FC0] text-gray-800  border-transparent rounded-none hover:bg-[#FC0] hover:text-white"
                  style={{
                    clipPath:
                      'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                    boxShadow:
                      '0px 30px 40px 0px rgba(0, 0, 0, 0.5), 0px 0px 20px 0px rgba(0, 0, 0, 0.5), 0px -10px 10px 0px rgba(255, 255, 255, 0.2)',
                  }}
                />
              )}
            </Carousel>
          ) : null}
        </div>
      ) : null}
      <Dialog open={openDialog} onOpenChange={setOpenDialog}>
        <DialogContent
          className="w-full sm:max-w-screen-xl p-2 shadow-none border-none [&>button]:hidden"
          style={{
            borderRadius: gallery_videos?.border_radius
              ? gallery_videos?.border_radius
              : 'unset',
          }}
        >
          <div className="w-full h-auto max-w-screen-xl">{renderVideoBox}</div>
        </DialogContent>
      </Dialog>
    </>
  )
}
