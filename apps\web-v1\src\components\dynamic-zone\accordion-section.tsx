'use client'

import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
  CmsMedia,
} from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import { Minus, Plus } from 'lucide-react'
import { useState } from 'react'
import { cn } from '~/src/utils'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'
import BlockEditor from '../renderer/block-editor'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
  accordion: any
}
//----------------------------------------------------------------------------------
export default function AccordionSection({
  styles,
  heading,
  accordion,
}: TProps) {
  console.log('🚀 ~ AccordionSection ~ accordion:', accordion)
  const [open, setOpen] = useState<number | null>(0)
  const {
    background_color,
    image,
    item_border_radius,
    show_order,
    text_color,
    items,
  } = accordion || {}

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <div className="flex flex-col-reverse  md:flex-row gap-10">
        <Accordion
          type="single"
          collapsible
          defaultValue={`${0}`}
          className={cn(
            'flex flex-col gap-y-4 ',
            image?.url ? 'w-full' : 'w-full md:w-1/2',
          )}
        >
          {items?.map((item: any, idx: number) => (
            <AccordionItem
              key={idx}
              value={`${idx}`}
              className="p-6 border-none"
              style={{
                borderRadius: item_border_radius || 0,
                color: text_color || '#182230',
                backgroundColor: background_color || 'transparent',
              }}
            >
              <AccordionTrigger
                className="[&>svg]:hidden p-0 hover:no-underline"
                onClick={() => setOpen(idx)}
              >
                <div className="w-full flex items-start justify-between gap-2">
                  <div className="flex gap-2">
                    {!show_order ? (
                      item?.icon?.url ? (
                        <CmsMedia
                          media={item?.icon}
                          className="min-w-6 min-h-6 w-6 h-6"
                        />
                      ) : null
                    ) : null}
                    <Typography
                      variant="body1"
                      className="text-gray-800 font-semibold"
                    >
                      {show_order ? <span>{idx + 1}.&nbsp;</span> : null}
                      {item?.title}
                    </Typography>
                  </div>
                  {open == idx ? (
                    <Minus className="text-gray-800 min-w-4 w-4 min-h-4 h-4 md:min-w-6 md:w-6 md:min-h-6 md:h-6 transition-all duration-300" />
                  ) : (
                    <Plus className="text-gray-800 min-w-4 w-4 min-h-4 h-4 md:min-w-6 md:w-6 md:min-h-6 md:h-6 transition-all duration-300" />
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent className="pt-4 pb-0">
                <BlockEditor content={item?.description} />
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
        {image?.url ? (
          <div
            className="w-full h-auto md:h-full md:min-h-[400px] aspect-[16/9] md:aspect-auto"
            style={{
              borderRadius: item_border_radius || 0,
              backgroundImage: `url(${RenderImageUrlStrapi({ url: image?.url })})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            }}
          ></div>
        ) : null}
      </div>
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
