'use client'

import { i18n } from '@lingui/core'
import { useEffect, useState } from 'react'
import { useLocale } from '../libs/data/use-locale'
import { getAppMessages } from './i18n'

export function useLinguiClient() {
  const locale = useLocale()
  const [messages, setMessages] = useState<any>(null)

  useEffect(() => {
    const loadMessages = async () => {
      const messages = await getAppMessages(locale)
      setMessages(messages)
    }
    loadMessages()
  }, [locale])

  useEffect(() => {
    if (messages) {
      i18n.load(locale, messages[locale])
      i18n.activate(locale)
    }
  }, [messages, locale])

  return i18n
}
