'use client'
import { TArticleSchema } from '@ttplatform/core-page-builder/libs'
import { useGetArticles } from '~/src/libs/cms/strapi/use-articles'
import { useLocale } from '~/src/libs/data/use-locale'
import { FeaturedNewsPromotions } from '~/src/modules/news-promotions'
import { EmptyContent } from '../../../../../../packages/ui/src/templates/empty'

type TProps = {
  limit: number
  custom_news: TArticleSchema[]
}

const NewsFeatured = ({ limit, custom_news }: TProps) => {
  const locale = useLocale()
  if (custom_news?.length > 0) {
    return <FeaturedNewsPromotions items={custom_news} type="news" />
  }

  const { data, isLoading, error } = useGetArticles({
    locale,
    filters: {
      featured: true,
    },
    pagination: {
      limit,
    },
  })

  if (isLoading) {
    return (
      <div className="w-12 h-12 border-4 border-gray-50 border-t-transparent rounded-full animate-spin"></div>
    )
  }

  const notFound = !isLoading && error && !data?.data.length

  if (notFound) {
    return <EmptyContent title="Đang cập nhật..." />
  }

  return <FeaturedNewsPromotions items={data?.data} type="news" />
}

export default NewsFeatured
