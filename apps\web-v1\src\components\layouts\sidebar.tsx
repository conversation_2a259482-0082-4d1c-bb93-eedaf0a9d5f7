import { SidebarInset, SidebarProvider } from '@ttplatform/ui/components'
import React from 'react'
import { AppSidebar } from './app-sidebar'

type T_SidebarProps = React.ComponentProps<typeof SidebarInset>

export default function Sidebar({ children, ...props }: T_SidebarProps) {
  return (
    <SidebarProvider className="bg-white">
      <AppSidebar variant="inset" />
      <SidebarInset className="border" {...props}>
        {children}
      </SidebarInset>
    </SidebarProvider>
  )
}
