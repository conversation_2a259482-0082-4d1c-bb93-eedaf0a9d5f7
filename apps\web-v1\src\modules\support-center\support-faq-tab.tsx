'use client'

import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
  BlockRendererClient,
} from '@ttplatform/core-page-builder/components'
import { TSupportCenterSchema } from '@ttplatform/core-page-builder/libs'
import { Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'

interface SupportFAQTabProps {
  items: TSupportCenterSchema[]
  isInView?: boolean
  accordionVariants?: any
  searchQuery?: string
}

export const SupportFAQTab = ({ items }: SupportFAQTabProps) => {
  if (!items?.length) return null

  return (
    <Accordion
      type="single"
      collapsible
      defaultValue="faq-1"
      className="flex flex-col gap-4"
    >
      {items?.map((item, index) => (
        <AccordionItem
          key={index}
          value={item.documentId}
          className="rounded-lg border-none bg-white last:mb-0 shadow-[0px_1px_2px_0px_rg<PERSON>(16,24,40,0.06),0px_1px_3px_0px_rgba(16,24,40,0.1)]"
        >
          <AccordionTrigger className="px-5 pl-6 py-5 data-[state=open]:pb-3 hover:no-underline cursor-pointer text-black">
            <Typography variant="body1" className="font-semibold">
              {item.name}
            </Typography>
          </AccordionTrigger>
          <AccordionContent className={cn('px-5 pl-6 pb-4 text-gray-600')}>
            <BlockRendererClient content={item.content} />
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  )
}
