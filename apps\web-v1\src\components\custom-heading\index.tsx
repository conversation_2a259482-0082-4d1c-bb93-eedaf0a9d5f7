import { MainButton } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'

export enum ButtonTarget {
  SELF = '_self',
  BLANK = '_blank',
  PARENT = '_parent',
  TOP = '_top',
}

export enum CustomHeadingType {
  GRID = 'grid',
  DEFAULT = 'default',
}

export interface CustomTextStyles {
  font_weight?: string
  color?: string
  text_align?: string
  variant?: string
}

export interface CustomText {
  text?: string
  styles?: CustomTextStyles
}

export interface CustomButton {
  text?: string
  url?: string
  target?: ButtonTarget
  variant?: string
}

export interface CustomHeadingProps {
  heading?: CustomText
  sub_heading?: CustomText
  buttons?: CustomButton[]
  locale?: string
  type: CustomHeadingType
  button_style?: string
}

const CustomHeading = ({
  heading,
  sub_heading,
  buttons,
  type,
  button_style,
}: CustomHeadingProps) => {
  return (
    <div className="w-full">
      {type === CustomHeadingType.DEFAULT &&
        renderLayoutDefault(heading, sub_heading, buttons, button_style)}
      {type === CustomHeadingType.GRID &&
        renderLayoutGrid(heading, sub_heading, buttons, button_style)}
    </div>
  )
}

const renderLayoutDefault = (
  heading?: CustomText,
  sub_heading?: CustomText,
  buttons?: CustomButton[],
  button_style?: string,
) => {
  const { text, styles } = heading || {}
  const { text: sub_text, styles: sub_styles } = sub_heading || {}
  return (
    <div className="flex flex-col gap-4">
      {heading && (
        <Typography
          style={{ color: styles?.color }}
          variant={styles?.variant as any}
          className={cn(styles?.text_align, styles?.font_weight)}
        >
          {text?.toUpperCase()}
        </Typography>
      )}
      {sub_heading && (
        <Typography
          style={{ color: sub_styles?.color }}
          variant={sub_styles?.variant as any}
          className={cn(sub_styles?.text_align, sub_styles?.font_weight)}
        >
          {sub_text}
        </Typography>
      )}
      {buttons && buttons.length > 0 && (
        <div className={cn('flex gap-4 items-center', button_style)}>
          {buttons.map((button, index) => {
            return (
              <MainButton
                key={index}
                label={button.text}
                url={button.url || ''}
                target={button.target as any}
                variant={button.variant as any}
              />
            )
          })}
        </div>
      )}
    </div>
  )
}

const renderLayoutGrid = (
  heading?: CustomText,
  sub_heading?: CustomText,
  buttons?: CustomButton[],
  button_style?: string,
) => {
  return (
    <div
      className={cn(
        sub_heading
          ? 'grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-12'
          : 'flex justify-between items-center',
      )}
    >
      <div className="flex flex-col gap-4">
        {heading && (
          <Typography
            variant={heading.styles?.variant as any}
            className={cn(
              heading.styles?.text_align,
              heading.styles?.font_weight,
            )}
          >
            {heading.text?.toUpperCase()}
          </Typography>
        )}
      </div>
      <div className="flex flex-col gap-4">
        {sub_heading && (
          <Typography
            variant={sub_heading.styles?.variant as any}
            className={cn(
              sub_heading.styles?.text_align,
              sub_heading.styles?.font_weight,
            )}
          >
            {sub_heading.text}
          </Typography>
        )}
        <div className="flex gap-4 items-center">
          {buttons && buttons.length > 0 && (
            <div className={cn('flex gap-4 items-center', button_style)}>
              {buttons.map((button, index) => (
                <MainButton
                  key={index}
                  label={button.text}
                  url={button.url || ''}
                  target={button.target as any}
                  variant={button.variant as any}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CustomHeading
