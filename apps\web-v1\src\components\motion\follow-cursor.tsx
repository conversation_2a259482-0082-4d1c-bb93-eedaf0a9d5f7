'use client'

import React, { useEffect, useRef } from 'react'

interface FollowCursorProps {
  color?: string
  zIndex?: string
}

const FollowCursor: React.FC<FollowCursorProps> = ({
  color = '#fdc700',
  zIndex = '60',
}) => {
  const svgRef = useRef<HTMLDivElement | null>(null)
  const animationFrameRef = useRef<number | null>(null)
  const cursorRef = useRef({ x: 0, y: 0 })
  const positionRef = useRef({ x: 0, y: 0 })
  const dimensionsRef = useRef({ width: 0, height: 0 })

  useEffect(() => {
    const prefersReducedMotion = window.matchMedia(
      '(prefers-reduced-motion: reduce)',
    )
    const lag = 8

    const updatePosition = () => {
      if (svgRef.current) {
        positionRef.current.x +=
          (cursorRef.current.x - positionRef.current.x) / lag
        positionRef.current.y +=
          (cursorRef.current.y - positionRef.current.y) / lag

        svgRef.current.style.transform = `translate(${positionRef.current.x}px, ${positionRef.current.y}px)`
      }
    }

    const loop = () => {
      updatePosition()
      animationFrameRef.current = requestAnimationFrame(loop)
    }

    const onMouseMove = (e: MouseEvent) => {
      cursorRef.current.x = e.clientX
      cursorRef.current.y = e.clientY
    }

    const onWindowResize = () => {
      dimensionsRef.current.width = window.innerWidth
      dimensionsRef.current.height = window.innerHeight
    }

    const init = () => {
      if (prefersReducedMotion.matches) {
        console.log('Reduced motion enabled, cursor effect skipped.')
        return
      }

      dimensionsRef.current = {
        width: window.innerWidth,
        height: window.innerHeight,
      }
      cursorRef.current = {
        x: dimensionsRef.current.width / 2,
        y: dimensionsRef.current.height / 2,
      }
      positionRef.current = {
        x: dimensionsRef.current.width / 2,
        y: dimensionsRef.current.height / 2,
      }

      const svgContainer = document.createElement('div')
      svgContainer.style.position = 'fixed'
      svgContainer.style.top = '0'
      svgContainer.style.left = '0'
      svgContainer.style.pointerEvents = 'none'
      svgContainer.style.zIndex = zIndex
      svgContainer.style.transform = `translate(${positionRef.current.x}px, ${positionRef.current.y}px)`

      svgContainer.innerHTML = `
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          style="color: ${color}; filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.15));"
        >
          <path
            fill="currentColor"
            d="M7.95 21q-.55 0-1-.262T6.225 20l-4.05-7q-.275-.475-.275-1t.275-1l4.05-7q.275-.475.725-.737t1-.263h8.1q.55 0 1 .263t.725.737l4.05 7q.275.475.275 1t-.275 1l-4.05 7q-.275.475-.725.738t-1 .262z"
          />
        </svg>
      `
      document.body.appendChild(svgContainer)
      svgRef.current = svgContainer

      window.addEventListener('mousemove', onMouseMove)
      window.addEventListener('resize', onWindowResize)
      loop()
    }

    const destroy = () => {
      if (svgRef.current) {
        svgRef.current.remove()
        svgRef.current = null
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
      window.removeEventListener('mousemove', onMouseMove)
      window.removeEventListener('resize', onWindowResize)
    }

    prefersReducedMotion.onchange = () => {
      if (prefersReducedMotion.matches) {
        destroy()
      } else {
        init()
      }
    }

    init()

    return () => {
      destroy()
    }
  }, [color, zIndex])

  return null
}

export default FollowCursor
