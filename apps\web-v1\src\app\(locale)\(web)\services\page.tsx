import { Metadata } from 'next'
import ClientSlugHandler from '~/src/app/(locale)/client-slug-handler'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import { initLingui } from '~/src/localization/i18n-init-lingui'
import { PageBreadcrumb } from '~/src/modules/layout'
// import ServerSiteHandler from '../../server-site-handler'

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pageService,
    params: {
      filters: {
        locale: locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = pageData?.seo
  const metadata = generateMetadataObject(seo, pageData?.title)
  return metadata
}

export default async function ServicesPage() {
  const locale = await getLocale()
  const i18n = initLingui(locale)

  const pageData = await fetchContentType({
    contentType: apiRoute.pageService,
    params: {
      filters: { locale: locale },
    },
    spreadData: true,
  })

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = ''
      return acc
    },
    { [locale]: '' },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />
      {/* <ServerSiteHandler /> */}
      <PageBreadcrumb items={[{ title: i18n._('Services') }]} />
      <PageContent pageData={pageData} />
    </>
  )
}
