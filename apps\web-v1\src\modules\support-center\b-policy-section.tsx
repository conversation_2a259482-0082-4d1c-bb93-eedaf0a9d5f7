'use client'

import { FeatureCard } from '@ttplatform/core-page-builder/components'

interface FeatureCardItem {
  title: string
  description: string
  actionLabel: string
  imageUrl: string
}

interface BPolicySectionProps {
  cards: FeatureCardItem[]
  className?: string
}

export const BPolicySection: React.FC<BPolicySectionProps> = ({
  cards,
  className,
}) => {
  return (
    <section
      className={`grid grid-cols-1 md:grid-cols-2 gap-10 ${className || ''}`}
    >
      {cards.map((card, index) => (
        <FeatureCard
          key={index}
          title={card.title}
          description={card.description}
          actionLabel={card.actionLabel}
          imageUrl={card.imageUrl}
        />
      ))}
    </section>
  )
}
