import AppTopBar from './app-topbar'

type T_Props = {
  children: React.ReactNode
  breadcrumb?: {
    title?: string
    items: {
      label: string
      href?: string
      icon?: React.ReactNode
      isActive?: boolean
    }[]
  }
}

export default function AppInset({ children, breadcrumb }: T_Props) {
  return (
    <>
      <AppTopBar {...breadcrumb} />
      <div className="flex flex-1 flex-col gap-4 p-4">{children}</div>
    </>
  )
}
