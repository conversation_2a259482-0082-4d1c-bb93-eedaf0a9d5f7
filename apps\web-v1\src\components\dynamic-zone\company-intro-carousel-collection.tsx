'use client'

import { Typography } from '@ttplatform/ui/components'
import { Swiper, SwiperSlide } from 'swiper/react'
import { useMediaQuery } from 'usehooks-ts'
import CustomSectionRender from '../custom-section-render'

import { MainButton } from '@ttplatform/core-page-builder/components'
import Image from 'next/image'
import { useMemo, useState } from 'react'
import CustomHeading, { CustomHeadingProps } from '../custom-heading'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'

export const BCompanyIntro: React.FC<{ items: any; locale?: string }> = ({
  items,
}) => {
  return (
    <>
      <Carousel slides={items} />
    </>
  )
}

const BCompanyIntroWithSchema = ({
  heading,
  items,
  styles,
  locale,
}: {
  heading: any
  items: any
  styles: any
  locale?: string
}) => {
  const renderContent = (
    <div className="flex flex-col gap-20">
      <CustomHeading
        {...heading}
        button_style="shadow-lg rounded-sm"
        locale={locale}
      />
      <BCompanyIntro items={items} locale={locale} />
    </div>
  )
  return (
    <CustomSectionRender styles={styles}>{renderContent}</CustomSectionRender>
  )
}

interface SlideItemProps {
  background?: any
  content?: CustomHeadingProps
}

const SlideItem = ({ background, content }: SlideItemProps) => {
  return (
    <div className="group relative w-full aspect-[2/3] overflow-hidden rounded-xl">
      <Image
        src={RenderImageUrlStrapi({ url: background.image?.[0].url }) || ''}
        alt={background.image?.[0]?.alternativeText || ''}
        fill
        className="object-cover rounded-xl transition-transform duration-300 w-full h-full group-hover:scale-105"
        priority
      />
      <div className="absolute inset-0 bg-black/40 flex flex-col justify-end p-8 rounded-xl">
        <Typography
          variant={content?.heading?.styles?.variant as any}
          style={{ color: content?.heading?.styles?.color }}
        >
          {content?.heading?.text}
        </Typography>
        <div className="sm:overflow-hidden">
          <Typography
            className="hidden my-5 max-w-md line-clamp-3 sm:block sm:opacity-0 sm:h-0 sm:my-0 sm:group-hover:opacity-100 sm:group-hover:h-auto sm:group-hover:my-5 transition-all duration-300"
            variant={content?.sub_heading?.styles?.variant as any}
            style={{ color: content?.sub_heading?.styles?.color }}
          >
            {content?.sub_heading?.text}
          </Typography>
          <div className="block sm:opacity-0 sm:h-0 sm:group-hover:opacity-100 sm:group-hover:h-auto transition-all duration-300">
            {content?.buttons?.map((button) => (
              <div className="flex flex-col gap-4" key={button.text}>
                <MainButton
                  className="w-full  md:w-[200px]"
                  label={button.text}
                  url={button.url || ''}
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

interface CarouselProps {
  slides: SlideItemProps[]
}

export function Carousel({ slides }: CarouselProps) {
  const isSm = useMediaQuery('(max-width: 640px)')
  const is2xl = useMediaQuery('(min-width: 1536px)')
  const is3xl = useMediaQuery('(min-width: 1920px)')
  const [isEnd, setIsEnd] = useState(false)

  const slidesOffsetBefore = useMemo(() => {
    if (is3xl) return 160
    if (is2xl) return 80
    if (isSm) return 12
    return 40
  }, [isSm, is2xl, is3xl])

  return (
    <div className="relative w-full overflow-hidden">
      <Swiper
        spaceBetween={24}
        slidesPerView={1.5}
        speed={600}
        slidesOffsetBefore={slidesOffsetBefore}
        pagination={{ clickable: true }}
        breakpoints={{
          640: { slidesPerView: 2, spaceBetween: 20 },
          1024: { slidesPerView: 2.5, spaceBetween: 24 },
          1280: { slidesPerView: 3.5, spaceBetween: 28 },
        }}
        className="w-full"
        onSlideChange={(swiper) => setIsEnd(swiper.isEnd)}
        onReachEnd={() => setIsEnd(true)}
        onFromEdge={() => setIsEnd(false)}
      >
        {slides.map((slide, index) => (
          <SwiperSlide key={index}>
            <SlideItem background={slide.background} content={slide.content} />
          </SwiperSlide>
        ))}
      </Swiper>
      <div
        className={`absolute top-0 right-0 h-full w-[150px] sm:w-[200px] lg:w-[300px] pointer-events-none z-10 transition-opacity duration-300 ${
          isEnd ? 'opacity-0' : 'opacity-100'
        }`}
        style={{
          background:
            'linear-gradient(to left, rgba(255, 255, 255, 0.7), transparent)',
        }}
      />
    </div>
  )
}

export default BCompanyIntroWithSchema
