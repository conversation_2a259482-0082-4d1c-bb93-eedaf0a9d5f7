'use client'

import { CmsMedia } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { motion } from 'framer-motion'
import { useEffect, useRef, useState } from 'react'

import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'
import HeadingSection from '../renderer/heading-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
  timeline: any
}
//----------------------------------------------------------------------------------
export default function TimelineSection({ styles, heading, timeline }: TProps) {
  const { item_background, item_border_radius, text_color, items } =
    timeline || {}
  const { background, content_max_width, padding } = styles || {}

  const sliderRef = useRef<HTMLDivElement>(null)
  const slidesRef = useRef<HTMLDivElement>(null)

  const [sliderWidth, setSliderWidths] = useState(0)
  const [slidesWidth, setSlidesWidths] = useState(0)

  const renderMaxWidth = (maxWidth: string) => {
    switch (maxWidth) {
      case 'full_Width':
        return ''
      case 'xl':
        return 'max-w-7xl px-4'
      case 'lg':
        return 'max-w-6xl px-4'
      case 'md':
        return 'max-w-5xl px-4'
      case 'sm':
        return 'max-w-4xl px-4'
      case 'xs':
        return 'max-w-3xl px-4'
    }
  }

  useEffect(() => {
    if (!sliderRef.current || !slidesRef.current) return

    const updateSizes = () => {
      setSliderWidths(sliderRef.current!.clientWidth)
      setSlidesWidths(slidesRef.current!.scrollWidth)
    }

    updateSizes()
    window.addEventListener('resize', updateSizes)
    return () => void window.removeEventListener('resize', updateSizes)
  }, [])

  const renderContent = (
    <div className="flex flex-col gap-y-10 relative">
      <div className={cn('w-full mx-auto', renderMaxWidth(content_max_width))}>
        <HeadingSection heading={heading} />
      </div>

      <div className="w-full h-full relative">
        <div
          ref={sliderRef}
          className="w-full overflow-x-auto cursor-grab scrollbar-hide"
        >
          <motion.div
            ref={slidesRef}
            drag="x"
            dragConstraints={{
              left: -(slidesWidth - sliderWidth),
              right: 0,
            }}
            dragElastic={0.2}
            dragTransition={{ bounceDamping: 18 }}
            whileTap={{ cursor: 'grabbing' }}
            className="min-w-max px-[100px] md:px-[150px] flex relative py-[500px] [&>*:nth-child(even)>*]:top-[50px] [&>*:nth-child(odd)>*]:bottom-[calc(100%+50px)] "
          >
            <div className="line-overlay absolute top-[50%] left-0 translate-y-[-50%] w-full h-[4px] bg-gray-500 z-90"></div>

            {items?.map((item: any, index: number) => (
              <TimelineItem
                item={item}
                key={index}
                background={item_background}
                border_radius={item_border_radius}
                text_color={text_color}
              />
            ))}
          </motion.div>
        </div>
      </div>
    </div>
  )
  return (
    <section
      data-section={'time line'}
      style={{
        backgroundImage: styles?.background?.image?.url
          ? `url(${RenderImageUrlStrapi({ url: background?.image?.url })})`
          : 'unset',
        backgroundColor: background?.color
          ? `rgb(from ${background?.color} r g b / ${background?.opacity})`
          : '#FFF',

        backgroundPosition: 'center center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundBlendMode: 'multiply',
        position: 'relative',
        paddingTop: padding?.top,
        paddingBottom: padding?.bottom,
        paddingLeft: padding?.left,
        paddingRight: padding?.right,
      }}
    >
      {renderContent}
    </section>
  )
}
//----------------------------------------------------------------------------------
type TTimelineItem = {
  background?: any
  border_radius?: any
  text_color?: any
  item?: any
}
function TimelineItem({
  item,
  background,
  border_radius,
  text_color,
}: TTimelineItem) {
  return (
    <div className="timeline-item group h-[4px] w-[250px] relative bg-gray-500 [&:nth-child(odd):before]:bottom-[100%] [&:nth-child(even):before]:top-0 before:absolute before:content-[''] before:z-50 before:left-[50%] before:translate-x-[-50%] before:w-[2px] before:h-[50px] before:bg-gray-500">
      <div className="card-item absolute w-[150%] left-[50%] translate-x-[-50%]">
        <div
          className="timeline-content p-4 flex flex-col gap-4  min-h-[426px]"
          style={{
            backgroundImage: background?.image?.url
              ? `url(${RenderImageUrlStrapi({ url: background?.image?.url })})`
              : 'unset',
            backgroundColor: background?.color
              ? `rgb(from ${background?.color} r g b / ${background?.opacity})`
              : '#FFF',

            backgroundPosition: 'center center',
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
            backgroundBlendMode: 'multiply',
            position: 'relative',
            borderRadius: border_radius,
          }}
        >
          {/* replace your bg-div with this */}
          <div
            className="relative w-full aspect-[16/9] overflow-hidden"
            style={{ borderRadius: border_radius }}
          >
            <CmsMedia
              media={item?.image}
              width={0}
              height={0}
              sizes="100vw"
              className="w-full h-full object-cover"
            />
          </div>

          <div
            className="flex flex-col gap-2 text-center"
            style={{ color: text_color }}
          >
            <div className="flex flex-col gap-1">
              <Typography variant="h5" weight={'bold'}>
                {item?.title}
              </Typography>
              <Typography
                variant="body2"
                weight={'semibold'}
                align={'center'}
                className="line-clamp-3"
              >
                {item?.sub_title}
              </Typography>
            </div>
            <Typography
              variant="body2"
              weight={'normal'}
              className="mt-1 line-clamp-4"
            >
              {item?.description}
            </Typography>
          </div>
        </div>
      </div>
    </div>
  )
}
