import {
  Carousel,
  CarouselContent,
  CarouselItem,
  Typography,
} from '@ttplatform/ui/components'
import Autoplay from 'embla-carousel-autoplay'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { useCallback, useEffect, useRef, useState } from 'react'
import fetchContentTypeClient from '~/src/libs/cms/strapi/fetchContentTypeClient'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
}
//----------------------------------------------------------------------------------
export default function OtherServicesSection({ styles, heading }: TProps) {
  // const cookieStore = await cookies()
  // const locale = cookieStore.get('NEXT_LOCALE')?.value ?? 'en'

  const plugin = useRef(Autoplay({ delay: 5000, stopOnInteraction: true }))

  const params = useParams()
  const { slug } = params
  const [otherServicesData, setOtherServicesData] = useState<any>(null)

  const fetchServicesData = useCallback(async (slugService: string) => {
    const data = await fetchContentTypeClient({
      contentType: 'services',
      params: {
        filters: {
          slug: {
            $ne: slugService,
          },
        },
      },
    })
    setOtherServicesData(data?.data)
  }, [])

  useEffect(() => {
    fetchServicesData(slug as string)
  }, [fetchServicesData, slug])

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <div>
        <Carousel
          plugins={[plugin.current]}
          opts={{
            align: 'start',
            loop: true,
          }}
          className="w-full relative"
        >
          <CarouselContent>
            {otherServicesData?.map((item: any, idx: number) => (
              <CarouselItem
                key={idx}
                className="basis-1/1 sm:basis-1/2 md:basis-1/3"
              >
                <ServiceItem item={item} />
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}

function ServiceItem({ item }: { item: any }) {
  return (
    <Link href={`/services/${item?.slug}`}>
      <div
        className="w-full flex flex-col rounded-lg overflow-hidden group"
        style={{
          boxShadow:
            '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
        }}
      >
        <div className="relative w-full h-auto aspect-[9/6] overflow-hidden ">
          <div
            className="w-full h-full group-hover:scale-110 ease-in-out transition-all duration-500"
            style={{
              backgroundImage: `url(${RenderImageUrlStrapi({ url: item?.banner?.url })})`,
              backgroundRepeat: 'no-repeat',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          />
          <div
            className="w-full h-auto absolute bottom-0 py-2 px-6"
            style={{
              background:
                'linear-gradient(90deg, rgba(255, 204, 0, 0.95) 70%, rgba(255, 204, 0, 0.85) 90%, rgba(255, 204, 0, 0.65) 100%)',
            }}
          >
            <Typography variant="h6" className="text-gray-800 font-semibold">
              {item?.name}
            </Typography>
          </div>
        </div>
        <div className="bg-white py-4 px-6">
          <Typography
            variant="body2"
            className="text-gray-700 text-justify line-clamp-3"
          >
            {item?.description}
          </Typography>
        </div>
      </div>
    </Link>
  )
}
