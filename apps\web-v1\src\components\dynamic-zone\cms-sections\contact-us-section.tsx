import { ContainerSection } from '@ttplatform/core-page-builder/components'
import { T_ContactUsSectionSchema } from '@ttplatform/core-page-builder/libs'
import { ContactDetails, ContactForm } from '~/src/modules/contact/components'

const ContactUsSection = (props: T_ContactUsSectionSchema) => {
  const { heading, styles, contact_info, buttons } = props

  return (
    <ContainerSection styles={styles || {}}>
      <section className="flex flex-col gap-8 md:gap-12">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
          <ContactDetails
            title={heading?.heading?.text}
            description={heading?.sub_heading?.text}
            contactInfo={contact_info}
          />
          <ContactForm buttons={buttons} />
        </div>
      </section>
    </ContainerSection>
  )
}

export default ContactUsSection
