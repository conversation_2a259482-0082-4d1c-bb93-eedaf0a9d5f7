{"kind": "collectionType", "collectionName": "promotion_categories", "info": {"singularName": "promotion-category", "pluralName": "promotion-categories", "displayName": "Promotion-Category"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "title": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "slug": {"type": "uid", "targetField": "title"}, "promotions": {"type": "relation", "relation": "oneToMany", "target": "api::promotion.promotion", "mappedBy": "category"}}}