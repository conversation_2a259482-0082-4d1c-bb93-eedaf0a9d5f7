import { MainButton } from '@ttplatform/core-page-builder/components'

import { Calendar1 } from 'lucide-react'

import { Typography } from '@ttplatform/ui/components'
import { fDateVIE } from '~/src/libs/cms/utils'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'

//---------------------------------------------------------------------------------
type CardProps = {
  article: any
}
//---------------------------------------------------------------------------------
export default function CardArticle({ article }: CardProps) {
  return (
    <div
      className="flex flex-col border border-gray-200 rounded-xl overflow-hidden group bg-white"
      style={{
        boxShadow:
          '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
      }}
    >
      <div className="w-full h-auto aspect-[9/6] overflow-hidden ">
        <div
          className="w-full h-full group-hover:scale-110 ease-in-out transition-all duration-500"
          style={{
            backgroundImage: `url(${RenderImageUrlStrapi({ url: article?.image?.url })})`,
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      </div>
      <div
        className="w-full h-[4px]"
        style={{
          background:
            'linear-gradient(90deg, #FC0 58.75%, #FFD326 83.64%, #FFDE59 100%)',
        }}
      />
      <div className="flex flex-col gap-4 p-6">
        <div className="flex flex-col gap-2">
          <Typography
            variant="h6"
            className="text-gray-800 line-clamp-2 font-semibold"
          >
            {article?.title}
          </Typography>
          <Typography variant="body1" className="text-gray-700 line-clamp-3">
            {article?.description}
          </Typography>
        </div>
        <div className="flex justify-between items-center">
          <div className="flex flex-col gap-2">
            <Typography
              variant="body1"
              className="text-[#FF9900] font-semibold"
            >
              {article?.category?.title}
            </Typography>
            <div className="flex items-center gap-2">
              <Calendar1 className="text-[#AF0E0E] w-5 h-5" />
              <Typography variant="body2" className="text-[#AF0E0E] font-bold">
                {fDateVIE(article?.createdAt)}
              </Typography>
            </div>
          </div>
          <MainButton />
        </div>
      </div>
    </div>
  )
}
