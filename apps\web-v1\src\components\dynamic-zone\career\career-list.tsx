'use client'

import { LayoutContainer } from '@ttplatform/core-page-builder/components'
import {
  THeadingSchema,
  TSectionStyleSchema,
} from '@ttplatform/core-page-builder/libs'
import Cookies from 'js-cookie'
import { useEffect, useState } from 'react'
import { useGetCareerCategories } from '~/src/libs/cms/strapi/use-careers'
import CareerList from '~/src/modules/career/career-list'

type TProps = {
  heading: THeadingSchema
  styles: TSectionStyleSchema
  locale: string
}

const CareersListView = ({ heading, styles }: TProps) => {
  const [jobList, setJobList] = useState<any>(null)
  const locale = Cookies.get('NEXT_LOCALE') ?? 'en'
  const { data } = useGetCareerCategories({
    locale,
  })

  useEffect(() => {
    if (data?.data) {
      setJobList(data?.data)
    }
  }, [data])

  return (
    <LayoutContainer>
      <CareerList heading={heading} styles={styles} data={jobList} />
    </LayoutContainer>
  )
}

export default CareersListView
