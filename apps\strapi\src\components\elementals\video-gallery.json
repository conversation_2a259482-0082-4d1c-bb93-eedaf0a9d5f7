{"collectionName": "components_elementals_video_galleries", "info": {"displayName": "Video_Gallery"}, "options": {}, "attributes": {"videos": {"type": "component", "component": "items.video-gallery-item", "repeatable": true}, "border_radius": {"type": "integer"}, "open_type": {"type": "enumeration", "default": "playin", "enum": ["playin", "dialog"]}, "loop": {"type": "boolean", "default": false}, "autoplay": {"type": "boolean", "default": false}}, "config": {}}