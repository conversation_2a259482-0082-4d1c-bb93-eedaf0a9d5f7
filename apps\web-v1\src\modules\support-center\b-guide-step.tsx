import {
  ArrowRightLargerIcon,
  HexIconWrapper,
  MainButton,
  SectionTitle,
} from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import Image from 'next/image'

interface IGuideStepProps {
  number?: string
  title?: string
  description?: string
  image: string
  alt: string
}

const steps = [
  {
    number: '01',
    title: 'ĐĂNG KÝ TÀI KHOẢN',
    description:
      'Tạo tài khoản mới để truy cập vào hệ thống và sử dụng đầy đủ các tính năng. Quá trình đăng ký đơn giản và nhanh chóng.',
    image:
      'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?q=80&w=600&auto=format&fit=crop',
    alt: 'Người dùng đang đăng ký tài khoản trên máy tính',
  },
  {
    number: '02',
    title: 'TÌM KIẾM PHỤ TÙNG',
    description:
      'T<PERSON><PERSON> kiếm phụ tùng một cách dễ dàng với công cụ tìm kiếm thông minh. <PERSON><PERSON><PERSON> theo danh m<PERSON>, th<PERSON><PERSON>ng hiệu hoặc mẫu xe.',
    image:
      'https://images.unsplash.com/photo-1512428559087-560fa5ceab42?q=80&w=600&auto=format&fit=crop',
    alt: 'Người dùng tìm kiếm sản phẩm trên điện thoại',
  },
  {
    number: '03',
    title: 'ĐẶT HÀNG TRỰC TUYẾN',
    description:
      'Thực hiện đặt hàng trực tuyến một cách nhanh chóng và an toàn. Theo dõi đơn hàng và nhận thông báo về trạng thái.',
    image:
      'https://images.unsplash.com/photo-1531482615713-2afd69097998?q=80&w=600&auto=format&fit=crop',
    alt: 'Người dùng đang đặt hàng trên máy tính',
  },
]

export const BGuideStep = ({
  sectionTitle = 'HƯỚNG DẪN SỬ DỤNG',
}: { sectionTitle?: string }) => {
  return (
    <section>
      <div className="mb-10 flex justify-between items-center">
        <SectionTitle text={sectionTitle} underlineWidth="w-16" />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {steps.map((step, index) => (
          <GuideStepCard
            key={index}
            number={step.number}
            title={step.title}
            description={step.description}
            image={step.image}
            alt={step.alt}
          />
        ))}
      </div>
    </section>
  )
}

export const GuideStepCard = ({
  number,
  title,
  description,
  image,
  alt,
}: IGuideStepProps) => {
  return (
    <div className="flex flex-col h-full bg-yellow-50 gap-8 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 relative">
      <ArrowRightLargerIcon className="absolute right-4 top-4" />
      <div className="p-6 flex-1 flex flex-col">
        <div className="flex flex-col gap-6">
          <HexIconWrapper
            content={number}
            sizeWrapper={60}
            classContent="font-bold text-2xl"
          />
          <Typography
            variant="h4"
            weight="bold"
            transform="uppercase"
            className="text-gray-800"
          >
            {title}
          </Typography>
        </div>
        <Typography variant="body1" className="text-gray-600 mb-4 mt-2">
          {description}
        </Typography>
        <div className="mt-auto">
          <MainButton label="XEM CHI TIẾT" variant="secondary" />
        </div>
      </div>

      <div className="flex-1 relative">
        <Image
          src={image}
          alt={alt}
          width={400}
          height={400}
          className="object-cover w-full h-full"
        />
      </div>
    </div>
  )
}
