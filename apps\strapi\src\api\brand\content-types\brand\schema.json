{"kind": "collectionType", "collectionName": "brands", "info": {"singularName": "brand", "pluralName": "brands", "displayName": "Distributed Brands"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "name": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "targetField": "name"}, "logo": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images"]}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "dynamic_zone": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["dynamic-zone.video-section", "dynamic-zone.related-products", "dynamic-zone.page-hero-section", "dynamic-zone.media-text-section", "dynamic-zone.latest-news-section", "dynamic-zone.hover-overlay-card-collection", "dynamic-zone.hover-expand-card-collection", "dynamic-zone.form-subscribe", "dynamic-zone.accordion-section", "dynamic-zone.featured-promotions", "dynamic-zone.image-box-section", "dynamic-zone.info-block", "dynamic-zone.icon-box-list-section", "dynamic-zone.timeline-section"]}}}