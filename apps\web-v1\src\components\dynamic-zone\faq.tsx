import { Heading } from '@/components/elements/heading'
import { IconHelpHexagonFilled } from '@tabler/icons-react'
import { Container } from '~/src/components/common/container'
import { FeatureIconContainer } from './features/feature-icon-container'

export const FAQ = ({
  heading,
  sub_heading,
  faqs,
}: { heading: string; sub_heading: string; faqs: any[] }) => {
  return (
    <Container className="flex flex-col items-center justify-between pb-20">
      <div className="relative z-20 py-10 md:pt-40">
        <FeatureIconContainer className="flex justify-center items-center overflow-hidden">
          <IconHelpHexagonFilled className="h-6 w-6 text-white" />
        </FeatureIconContainer>
        <Heading as="h1" className="mt-4">
          {heading}
        </Heading>
        {sub_heading && (
          <h4 className="text-sm md:text-base  max-w-4xl my-4 mx-auto text-muted text-center font-normal">
            {sub_heading}
          </h4>
        )}
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-10 py-20">
        {faqs &&
          faqs.map((faq: { question: string; answer: string }) => (
            <div key={faq.question}>
              <h4 className="text-lg font-bold bg-clip-text text-transparent bg-linear-to-b from-white to-neutral-400">
                {faq.question}
              </h4>
              <p className="mt-4 text-neutral-400">{faq.answer}</p>
            </div>
          ))}
      </div>
    </Container>
  )
}
