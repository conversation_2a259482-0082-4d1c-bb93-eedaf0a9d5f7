'use client'

import { Input } from '@ttplatform/ui/components'
import { ArrowRight, SearchIcon } from 'lucide-react'
import React, { useMemo, useState } from 'react'
import { HexIconWrapper } from '../../../atoms'

interface TechnicalDoc {
  id: string
  name: string
}

interface SupportTechnicalTabProps {
  technicalDocs: TechnicalDoc[]
}

export const SupportTechnicalTab = ({
  technicalDocs,
}: SupportTechnicalTabProps) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [submittedQuery, setSubmittedQuery] = useState('')

  const handleSearch = () => {
    setSubmittedQuery(searchQuery.trim())
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') handleSearch()
  }

  const filteredDocs = useMemo(() => {
    if (!submittedQuery) return technicalDocs
    return technicalDocs.filter((doc) =>
      doc.name.toLowerCase().includes(submittedQuery.toLowerCase()),
    )
  }, [technicalDocs, submittedQuery])

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="relative flex-1 mb-6">
        <Input
          type="text"
          placeholder="Tìm tài liệu theo tên, model sản phẩm liên quan, danh mục sản phẩm, ngành công nghiệp..."
          className="w-full rounded-md border shadow-sm border-gray-100 pl-9 pr-10 py-2 h-10 lg:h-13 text-sm sm:text-base "
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyDown={handleKeyDown}
        />
        <SearchIcon
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"
          size={16}
        />
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <HexIconWrapper
            IconComponent={ArrowRight as any}
            sizeWrapper={36}
            size={16}
            onClick={handleSearch}
          />
        </div>
      </div>
      <ul className="divide-y">
        {filteredDocs.length === 0 ? (
          <li className="py-6 text-center text-gray-400">
            Không tìm thấy tài liệu phù hợp
          </li>
        ) : (
          filteredDocs.map((doc) => (
            <li
              key={doc.id}
              className="flex items-center justify-between py-3 px-2"
            >
              <span className="truncate text-sm md:text-base">{doc.name}</span>
              <button
                className="ml-4 text-yellow-400 text-2xl"
                title="Download"
              >
                <svg width="28" height="28" fill="none" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M12 3v12m0 0l-4-4m4 4l4-4m-9 7h10"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>
            </li>
          ))
        )}
      </ul>
    </div>
  )
}
