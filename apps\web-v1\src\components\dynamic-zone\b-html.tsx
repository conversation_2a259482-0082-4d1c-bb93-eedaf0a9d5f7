'use client'

import { TSectionStyleSchema } from '@ttplatform/core-page-builder/libs'
import { cn } from '~/src/utils'

type TProps = {
  is_open: boolean
  json: {
    tag: string
    class?: string
    style: Record<string, any>
  }
  styles?: TSectionStyleSchema
  children?: React.ReactNode
}

const BHtml = ({ children, json, styles }: TProps) => {
  const { background, content_max_width, margin, padding } = styles || {}
  const { class: className, style } = json || {}

  const {
    top: marginTop,
    right: marginRight,
    bottom: marginBottom,
    left: marginLeft,
    unit: marginUnit,
  } = margin || {}

  const {
    top: paddingTop,
    right: paddingRight,
    bottom: paddingBottom,
    left: paddingLeft,
    unit: paddingUnit,
  } = padding || {}

  const mUnit = marginUnit == 'percent' ? '%' : marginUnit
  const pUnit = paddingUnit == 'percent' ? '%' : paddingUnit

  const marginCSS = {
    ...(marginTop && { marginTop: marginTop + mUnit }),
    ...(marginRight && { marginRight: marginRight + mUnit }),
    ...(marginBottom && { marginBottom: marginBottom + mUnit }),
    ...(marginLeft && { marginLeft: marginLeft + mUnit }),
  }

  const paddingCSS = {
    ...(paddingTop && { paddingTop: paddingTop + pUnit }),
    ...(paddingRight && { paddingRight: paddingRight + pUnit }),
    ...(paddingBottom && { paddingBottom: paddingBottom + pUnit }),
    ...(paddingLeft && { paddingLeft: paddingLeft + pUnit }),
  }

  const backgroundCSS = {
    ...(background?.color && { backgroundColor: background.color }),
    ...(background?.opacity && { opacity: background.opacity }),
    ...(background?.repeat && { backgroundRepeat: background.repeat }),
    ...(background?.size && { backgroundSize: background.size }),
  }

  return (
    <div
      data-block="open-html"
      className={cn(
        'w-full mx-auto',
        content_max_width === 'full_width' && 'max-w-full',
        content_max_width === 'container' && 'max-w-3xl',
        content_max_width === 'container-lg' && 'max-w-lg',
        content_max_width === 'container-xl' && 'max-w-xl',
        className,
      )}
      style={{
        ...backgroundCSS,
        ...marginCSS,
        ...paddingCSS,
        ...style,
      }}
    >
      {children}
    </div>
  )
}

export default BHtml
