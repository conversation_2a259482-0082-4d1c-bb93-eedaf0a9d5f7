import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '@ttplatform/ui/components'
import Autoplay from 'embla-carousel-autoplay'
import { useRef } from 'react'
import CardArticle from '../card/card-article'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
  items: any
}
//----------------------------------------------------------------------------------
export default function NewsRelated({ styles, heading, items }: TProps) {
  const plugin = useRef(Autoplay({ delay: 5000, stopOnInteraction: true }))

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <div>
        <Carousel
          plugins={[plugin.current]}
          opts={{
            align: 'start',
            loop: true,
          }}
          className="w-full relative"
        >
          <CarouselContent>
            {items?.map((item: any, idx: number) => (
              <CarouselItem
                key={idx}
                className="basis-1/1 sm:basis-1/2 lg:basis-1/3"
              >
                <CardArticle article={item} />
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
