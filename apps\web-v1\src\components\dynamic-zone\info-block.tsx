import { CmsMedia } from '@ttplatform/core-page-builder/components'
import { cn } from '~/src/utils'
import BlockEditor from '../renderer/block-editor'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  media_image: any
  image_position: any
  info_list: any
}
//----------------------------------------------------------------------------------
export default function InfoBlock({
  styles,
  media_image,
  image_position,
  info_list,
}: TProps) {
  const renderClassName = (image_position: string) => {
    switch (image_position) {
      case 'left':
        return ''
      case 'right':
        return 'md:flex-row-reverse'
    }
  }
  const renderContent = (
    <div
      className={cn(
        'flex flex-col md:flex-row gap-5 md:gap-8 lg:gap-10 items-center h-full',
        renderClassName(image_position),
      )}
    >
      <div className="w-full md:w-1/2 h-full">
        <CmsMedia
          media={media_image?.image}
          width={0}
          height={0}
          sizes="100vw"
          className={cn(
            'w-full h-full object-cover',
            media_image?.border_radius
              ? `rounded-[${media_image?.border_radius}px]`
              : '',
          )}
        />
      </div>
      <div className="w-full md:w-1/2">
        {info_list?.map((item: any, idx: number) => (
          <InfoItem key={idx} item={item} />
        ))}
      </div>
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}

function InfoItem({ item }: { item: any }) {
  const { heading, description } = item || {}
  return (
    <div>
      <div className="flex flex-col gap-5 md:gap-10">
        {heading ? <HeadingSection heading={heading} /> : null}

        {description ? <BlockEditor content={description} /> : null}
      </div>
    </div>
  )
}
