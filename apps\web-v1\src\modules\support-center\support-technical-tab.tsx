'use client'

import { DownloadFileIcon } from '@ttplatform/core-page-builder/components'
import { TSupportCenterSchema } from '@ttplatform/core-page-builder/libs'
import { Separator, Typography } from '@ttplatform/ui/components'
import Link from 'next/link'

interface SupportTechnicalTabProps {
  items: TSupportCenterSchema[]
  isInView: boolean
  accordionVariants: any
  searchQuery?: string
}

export const SupportTechnicalTab = ({
  items,
  searchQuery: parentSearchQuery = '',
}: SupportTechnicalTabProps) => {
  // Search is now handled by parent SearchBar component

  const renderContent = (
    <ul className="flex flex-col gap-4 2xl:gap-6">
      {items?.length > 0 ? (
        items.map((item, index) => (
          <>
            <li key={index} className="flex items-center justify-between">
              <Typography className="font-semibold" variant="body1">
                {item.name}
              </Typography>
              <div className="flex items-center gap-2">
                {item.document_files?.map((file, idx: number) => (
                  <Link key={idx} href={file.url || ''} target="_blank">
                    <button
                      className="ml-4 text-yellow-400 text-2xl"
                      title="Download"
                    >
                      <DownloadFileIcon />
                    </button>
                  </Link>
                ))}
              </div>
            </li>
            {index < items.length - 1 && <Separator />}
          </>
        ))
      ) : parentSearchQuery.trim() ? (
        <li className="py-12 text-center">
          <div className="flex flex-col items-center gap-4">
            <div className="text-6xl">📄</div>
            <Typography variant="h6" className="text-gray-600 font-semibold">
              Không tìm thấy tài liệu nào
            </Typography>
            <Typography variant="body2" className="text-gray-500">
              Không có tài liệu nào phù hợp với từ khóa "{parentSearchQuery}"
            </Typography>
            <Typography variant="body2" className="text-gray-400">
              Hãy thử tìm kiếm với từ khóa khác
            </Typography>
          </div>
        </li>
      ) : (
        <li className="py-8 text-center">
          <Typography variant="body2" className="text-gray-500">
            Chưa có tài liệu nào trong chủ đề này
          </Typography>
        </li>
      )}
    </ul>
  )

  return renderContent
}
