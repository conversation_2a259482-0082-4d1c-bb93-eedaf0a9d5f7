import { cmsContentType } from '../../constants/cms'
import { QueryParams, useClientFetch } from './use-client-fetch'

const CNT_PRODUCTS = cmsContentType.products
const CNT_PRODUCT_CATEGORIES = cmsContentType.productCategories
const CNT_PRODUCT_TYPES = cmsContentType.productTypes
const CNT_PRODUCT_MODELS = cmsContentType.productModels
const CNT_PRODUCT_SPECIFICATIONS = cmsContentType.productSpecifications

// #########################################################################################
// PRODUCTS
// #########################################################################################

/**
 * GET LIST PRODUCTS
 */
export const useGetProducts = ({
  filters,
  locale,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_PRODUCTS,
    params: {
      filters,
      locale,
      sort,
      pagination,
      populate,
      fields,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}

// #########################################################################################
// PRODUCT CATEGORIES
// #########################################################################################

export const useGetProductCategories = ({
  filters,
  locale,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_PRODUCT_CATEGORIES,
    params: {
      filters,
      locale,
      sort,
      pagination,
      populate,
      fields,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}

// #########################################################################################
// PRODUCT TYPES
// #########################################################################################

export const useGetProductTypes = ({
  filters,
  locale,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_PRODUCT_TYPES,
    params: {
      filters,
      locale,
      sort,
      pagination,
      populate,
      fields,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}

// #########################################################################################
// PRODUCT MODELS
// #########################################################################################

export const useGetProductModels = ({
  filters,
  locale,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_PRODUCT_MODELS,
    params: {
      filters,
      locale,
      sort,
      pagination,
      populate,
      fields,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}

// #########################################################################################
// PRODUCT SPECIFICATIONS
// #########################################################################################

export const useGetProductSpecifications = ({
  filters,
  locale,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_PRODUCT_SPECIFICATIONS,
    params: {
      filters,
      locale,
      sort,
      pagination,
      populate,
      fields,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}
