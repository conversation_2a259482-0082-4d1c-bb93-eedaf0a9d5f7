export default function BuilderPage() {
  return (
    <div className="flex flex-col h-screen w-screen bg-gray-50">
      <div className="flex items-center justify-between px-6 py-4 h-16 bg-white border-b border-gray-200">
        <span className="text-lg font-semibold text-gray-800">Topbar</span>
      </div>

      <div className="flex flex-1 overflow-hidden">
        <div className="w-64 bg-white border-r border-gray-200 p-4">
          <span className="text-gray-700">Leftbar</span>
        </div>

        <div className="flex flex-row justify-between w-full">
          <div className="w-full p-6 overflow-auto">
            <span className="text-gray-700">Main</span>
          </div>
          <div className="w-64 bg-white border-l border-gray-200 p-4">
            <span className="text-gray-700">Toolbar</span>
          </div>
        </div>
      </div>
    </div>
  )
}
