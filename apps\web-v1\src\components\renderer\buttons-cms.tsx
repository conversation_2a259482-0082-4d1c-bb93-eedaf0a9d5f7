import { cn } from '~/src/utils'
import MainButton from '../../../../../packages/core/page-builder/src/components/atoms/shared/main-button'

export default function ButtonsCms({
  buttons,
  className,
  isDisabledIcon = true,
}: { buttons: any; className?: string; isDisabledIcon?: boolean }) {
  return (
    <div className={cn('flex gap-4 flex-wrap', className)}>
      {buttons?.map((button: any, idx: number) => (
        <MainButton
          key={idx}
          variant={button?.variant || 'primary'}
          isDisabledIcon={isDisabledIcon}
          label={button?.text}
          url={button?.url}
          openInNewTab={button?.target}
        />
      ))}
    </div>
  )
}
