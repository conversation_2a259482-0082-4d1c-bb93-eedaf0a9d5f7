{"kind": "collectionType", "collectionName": "career_categories", "info": {"singularName": "career-category", "pluralName": "career-categories", "displayName": "Career-Category"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "title": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "targetField": "title"}, "jobs": {"type": "relation", "relation": "oneToMany", "target": "api::career.career", "mappedBy": "job_department"}}}