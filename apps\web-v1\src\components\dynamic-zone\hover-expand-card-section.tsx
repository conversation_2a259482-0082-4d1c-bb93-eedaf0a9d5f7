'use client'
import { ExpandCardSlider } from '@ttplatform/core-page-builder/components'
import { useEffect, useRef, useState } from 'react'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'

const HoverExpandCardSection = ({
  heading,
  items,
  styles,
}: {
  heading: any
  items: any
  styles: any
  locale?: string
}) => {
  const contentRef = useRef<HTMLDivElement>(null)
  const [contentWidth, setContentWidth] = useState(0)

  useEffect(() => {
    if (contentRef.current) {
      setContentWidth(contentRef.current.clientWidth)
    }
  }, [])

  const renderContent = (
    <div className="h-[700px] md:h-[950px] 3xl:h-[1138px]" ref={contentRef}>
      <HeadingSection heading={heading} isDisabledIcon={false}></HeadingSection>
      <div className="absolute 3xl:top-[32%] md:top-[38%] top-[52%] left-0 w-full z-10">
        <ExpandCardSlider slides={items} contentWidth={contentWidth} />
      </div>
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}

export default HoverExpandCardSection
