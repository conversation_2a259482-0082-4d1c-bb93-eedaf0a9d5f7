import { Skeleton } from '@ttplatform/ui/components'
import { useGetWidgets } from '~/src/libs/cms/strapi/use-widget'
import DynamicZoneManager from './manager'

type TProps = {
  id: string
  locale: string
}

const WidgetSection = ({ id, locale }: TProps) => {
  if (!id) return null

  const { data, isLoading, error } = useGetWidgets({
    filters: {
      id: id,
    },
  })

  const dynamicZone = data?.data?.[0]?.dynamic_zone || []
  const notFound = (!data?.data?.length || error) && !isLoading

  if (notFound) return null

  if (isLoading) return <Skeleton className="h-[100px] w-full" />

  return <DynamicZoneManager dynamicZone={dynamicZone} locale={locale} />
}

export default WidgetSection
