import { Input } from '@ttplatform/ui/components'
import { ArrowRight, SearchIcon } from 'lucide-react'
import Image from 'next/image'
import React, { useMemo, useState } from 'react'
import { HexIconWrapper } from '../../../atoms'

interface VideoItem {
  id: string
  title: string
  thumbnail: string
}

interface SupportVideoTabProps {
  videoList: VideoItem[]
}

export const SupportVideoTab: React.FC<SupportVideoTabProps> = ({
  videoList,
}) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [submittedQuery, setSubmittedQuery] = useState('')

  const handleSearch = () => {
    setSubmittedQuery(searchQuery.trim())
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') handleSearch()
  }

  const filteredVideos = useMemo(() => {
    if (!submittedQuery) return videoList
    return videoList.filter((video) =>
      video.title.toLowerCase().includes(submittedQuery.toLowerCase()),
    )
  }, [videoList, submittedQuery])

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="relative flex-1 mb-6">
        <Input
          type="text"
          placeholder="Tìm video hướng dẫn"
          className="w-full rounded-md border shadow-sm border-gray-100 pl-9 pr-10 py-2 h-10 lg:h-13 text-sm sm:text-base "
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyDown={handleKeyDown}
        />
        <SearchIcon
          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"
          size={16}
        />
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <HexIconWrapper
            IconComponent={ArrowRight}
            sizeWrapper={36}
            size={16}
            onClick={handleSearch}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {filteredVideos.length === 0 ? (
          <div className="col-span-full py-6 text-center text-gray-400">
            Không tìm thấy video phù hợp
          </div>
        ) : (
          filteredVideos.map((video) => (
            <div
              key={video.id}
              className="rounded-lg overflow-hidden shadow bg-white flex flex-col"
            >
              <div className="relative aspect-video w-full">
                <Image
                  src={video.thumbnail || '/images/fallback.png'}
                  alt="video thumbnail"
                  width={400}
                  height={225}
                  className="object-cover w-full h-full"
                  onError={(e) => {
                    const target = e.currentTarget as HTMLImageElement
                    if (target.src !== '/images/fallback.png')
                      target.src = '/images/fallback.png'
                  }}
                  priority
                />
                <button className="absolute inset-0 flex items-center justify-center text-white text-4xl">
                  <svg width="48" height="48" fill="none" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="12" fill="rgba(0,0,0,0.4)" />
                    <polygon points="10,8 16,12 10,16" fill="white" />
                  </svg>
                </button>
              </div>
              <div className="p-3 bg-yellow-50 text-xs md:text-sm font-medium truncate">
                {video.title}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}
