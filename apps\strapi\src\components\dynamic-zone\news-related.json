{"collectionName": "components_dynamic_zone_news_relateds", "info": {"displayName": "News_Related"}, "options": {}, "attributes": {"adminLabel": {"type": "string", "private": true}, "styles": {"type": "component", "component": "styles.section-styles", "repeatable": false}, "heading": {"type": "component", "component": "elementals.heading", "repeatable": false}, "news": {"type": "relation", "relation": "oneToMany", "target": "api::article.article"}}, "config": {}}