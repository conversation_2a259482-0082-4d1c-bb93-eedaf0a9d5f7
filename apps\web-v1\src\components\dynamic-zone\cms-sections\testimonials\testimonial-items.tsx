'use client'

import { Icon } from '@iconify/react'
import {
  CmsMedia,
  HexIconWrapper,
} from '@ttplatform/core-page-builder/components'
import {
  T_TestimonialsSectionSchema,
  blocksToHtml,
} from '@ttplatform/core-page-builder/libs'
import { SafeHTML } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { motion, useInView } from 'framer-motion'
import { ArrowLeft, ArrowRight } from 'lucide-react'
import { useCallback, useRef, useState } from 'react'
import HeadingSection from '~/src/components/renderer/heading-section'
import StylesSection from '~/src/components/renderer/styles-section'
import { LINGUI_CONFIG } from '~/src/config-global'

export default function TestimonialItems({
  items = [],
  heading,
  locale = LINGUI_CONFIG.defaultLocale,

  styles,
}: T_TestimonialsSectionSchema) {
  const testimonialsRef = useRef(null)
  const isTestimonialsInView = useInView(testimonialsRef, {
    once: true,
    margin: '-100px',
  })

  const [currentIndex, setCurrentIndex] = useState(0)

  const testimonialsData = items

  const handlePrev = useCallback(() => {
    setCurrentIndex((prev) =>
      prev === 0 ? testimonialsData.length - 1 : prev - 1,
    )
  }, [testimonialsData.length])

  const handleNext = useCallback(() => {
    setCurrentIndex((prev) =>
      prev === testimonialsData.length - 1 ? 0 : prev + 1,
    )
  }, [testimonialsData.length])

  const fadeInUp = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.4, 0, 0.2, 1],
        staggerChildren: 0.15,
      },
    },
  }

  const slideTransition = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.4, 0, 0.2, 1],
      },
    },
    exit: {
      opacity: 0,
      y: -30,
      transition: {
        duration: 0.6,
        ease: [0.4, 0, 0.2, 1],
      },
    },
  }

  if (!testimonialsData || testimonialsData.length === 0) {
    return null
  }

  const currentTestimonial = testimonialsData[currentIndex]

  return (
    <StylesSection styles={styles || {}}>
      {/* Customer Testimonials Section */}
      <div className="relative z-10" ref={testimonialsRef}>
        <motion.div
          className=" flex flex-col md:flex-row md:justify-between md:items-center mb-8 gap-6"
          initial="hidden"
          animate={isTestimonialsInView ? 'visible' : 'hidden'}
          variants={fadeInUp as any}
        >
          {heading && (
            <HeadingSection
              heading={heading}
              // fallbackHeading="CUSTOMER TESTIMONIALS"
            />
          )}
        </motion.div>

        <div className="flex flex-col md:flex-row h-full">
          {currentTestimonial?.image && (
            <motion.div
              className="md:w-1/2"
              initial="hidden"
              animate={isTestimonialsInView ? 'visible' : 'hidden'}
              variants={fadeInUp as any}
            >
              {/* TODO: FIXME IMG_CHANGE */}
              <CmsMedia
                media={currentTestimonial.image}
                className="w-full rounded-tl-xl rounded-tr-xl md:rounded-bl-md md:rounded-tr-none object-cover md:h-[525px]"
              />
              {/* <Image
                src={getMainImageUrl(currentTestimonial.image)}
                alt={
                  currentTestimonial.image?.alternativeText || 'PTC - Trusted'
                }
                width={600}
                height={600}
                className="w-full rounded-tl-xl rounded-tr-xl md:rounded-bl-md md:rounded-tr-none object-cover md:h-[525px]"
              /> */}
            </motion.div>
          )}
          <motion.div
            className={`p-6 xl:p-10 rounded-tr-md rounded-br-md bg-white 3xl:p-12 flex flex-col ${
              currentTestimonial?.image ? 'md:w-1/2' : 'w-full'
            }`}
            initial="hidden"
            animate={isTestimonialsInView ? 'visible' : 'hidden'}
            variants={fadeInUp as any}
          >
            <motion.div
              key={currentIndex}
              variants={slideTransition as any}
              initial="hidden"
              animate="visible"
              exit="exit"
              className="flex flex-col h-full"
            >
              <motion.div className="mb-2 flex" variants={fadeInUp as any}>
                {Array.from({ length: 5 }).map((_, index) => (
                  <span key={index} className="text-[#FFA500]">
                    <Icon icon="ic:baseline-star" width="20" height="20" />
                  </span>
                ))}
              </motion.div>
              <motion.div
                className="my-4 text-sm text-[#333333]"
                variants={fadeInUp as any}
              >
                <SafeHTML
                  html={blocksToHtml(currentTestimonial?.text || [], {
                    responsive: true,
                    className: 'testimonial-content',
                    locale,
                  })}
                  className="text-base sm:text-lg text-black !leading-7"
                />
              </motion.div>
              <motion.div
                className="flex flex-col sm:flex-row items-center gap-2 mt-auto"
                variants={fadeInUp as any}
              >
                <div className="flex gap-2 items-center">
                  {currentTestimonial?.user?.image ? (
                    <div
                      className={cn(
                        'relative z-10 3xl:h-[100px] 3xl:w-[112px] h-[50px] w-[56px] cursor-pointer overflow-hidden shrink-0',
                      )}
                      style={{
                        clipPath:
                          'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                      }}
                    >
                      <div
                        className="absolute top-1/2 left-1/2 z-20 h-[90px] w-[100px] -translate-x-1/2 -translate-y-1/2"
                        style={{ clipPath: 'inherit' }}
                      >
                        <CmsMedia
                          media={currentTestimonial.user.image}
                          className="object-cover"
                          contentClassName="h-full"
                          width={100}
                          height={100}
                          priority
                          fill
                        />
                      </div>
                      {/* <Image
                        src={getAvatarUrl(currentTestimonial.user.image)}
                        alt={`${currentTestimonial?.user?.name || 'User'} profile`}
                        width={100}
                        height={100}
                        className="object-cover"
                        loading="lazy"
                      /> */}
                    </div>
                  ) : (
                    // Placeholder avatar when user has no image
                    <div
                      className={cn(
                        'relative z-10 3xl:h-[100px] 3xl:w-[112px] h-[50px] w-[56px] cursor-pointer overflow-hidden shrink-0 bg-gray-200 flex items-center justify-center',
                      )}
                      style={{
                        clipPath:
                          'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                      }}
                    >
                      <span className="text-gray-500 text-lg font-semibold">
                        {currentTestimonial?.user?.name?.charAt(0) || 'U'}
                      </span>
                    </div>
                  )}
                  <div>
                    <p className="text-xl font-medium">
                      {currentTestimonial?.user?.name || ''}
                    </p>
                    <p className="mt-1 text-base text-gray-600">
                      {currentTestimonial?.user?.job || ''}
                    </p>
                  </div>
                </div>

                {/* Navigation */}
                <div className="ml-auto flex gap-1.5">
                  <HexIconWrapper
                    IconComponent={ArrowLeft}
                    sizeWrapper={40}
                    size={18}
                    color="text-gray-700"
                    onClick={handlePrev}
                  />
                  <HexIconWrapper
                    IconComponent={ArrowRight}
                    sizeWrapper={40}
                    size={18}
                    color="text-gray-700"
                    onClick={handleNext}
                  />
                </div>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </StylesSection>
  )
}
