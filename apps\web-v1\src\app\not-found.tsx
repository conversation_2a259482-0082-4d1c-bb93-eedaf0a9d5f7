import { <PERSON><PERSON>, <PERSON>, CardContent } from '@ttplatform/ui/components'
import { <PERSON>, Ghost, X } from 'lucide-react'
import { Metadata } from 'next'
import Image from 'next/image'
import Link from 'next/link'

export const metadata: Metadata = {
  title: '404',
  description: 'Something went wrong',
}

const floatingIcons = [
  {
    icon: <Ban className="h-8 w-8 text-gray-300" />,
    position: 'top-[30%] left-0',
    animation: 'animate-float-slow',
    rotate: 'rotate-12',
  },
  {
    icon: <X className="h-8 w-8 text-gray-300" />,
    position: 'top-[20%] left-[30%]',
    animation: 'animate-float',
    rotate: '-rotate-12',
  },
  {
    icon: <Ghost className="h-8 w-8 text-gray-300" />,
    position: 'top-[35%] right-[20%]',
    animation: 'animate-float-medium',
    rotate: 'rotate-6',
  },
]

const errorDigits = ['4', '0', '4']

export default function NotFound() {
  return (
    <div className="flex min-h-screen w-full items-center justify-center overflow-hidden bg-gray-50">
      <div className="relative mx-auto flex w-full max-w-xl flex-col items-center">
        <Image
          src="/images/not-found.webp"
          className="h-full w-full -translate-x-4 object-cover"
          alt="404"
          priority
          width={500}
          height={500}
          unoptimized
        />

        {floatingIcons.map(({ icon, position, animation, rotate }, index) => (
          <Card
            key={index}
            className={`absolute ${position} ${animation} ${rotate} flex h-16 w-16 items-center justify-center rounded-2xl bg-white shadow-lg`}
          >
            <CardContent className="p-0">{icon}</CardContent>
          </Card>
        ))}

        <div className="flex items-center justify-center gap-2">
          {errorDigits.map((digit, index) => (
            <Card
              key={index}
              className="flex h-24 w-24 items-center justify-center rounded-2xl bg-white shadow-lg"
            >
              <CardContent className="p-0">
                <span className="text-7xl font-bold text-gray-200">
                  {digit}
                </span>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-8 flex flex-col items-center text-center">
          <h1 className="text-2xl font-medium text-gray-700">Page not found</h1>
          <p className="mt-2 text-gray-500">
            The page you&apos;re looking for doesn&apos;t exist or has been
            moved.
          </p>

          <Link href="/">
            <Button className="mt-8 font-medium text-white">
              Go to frontpage
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
