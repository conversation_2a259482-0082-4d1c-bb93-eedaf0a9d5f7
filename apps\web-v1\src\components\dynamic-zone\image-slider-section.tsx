import { MarqueeImage } from '@ttplatform/core-page-builder/components'
import { TImageSliderFields } from '@ttplatform/core-page-builder/libs'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'

const ImageSliderSection = ({
  heading,
  styles,
  slider,
}: TImageSliderFields) => {
  const renderContent = (
    <div>
      <div className="flex flex-col gap-4 h-[400px] md:h-[700px]">
        <HeadingSection heading={heading || {}} />
        <div className="absolute top-1/2 md:top-[28%] left-0 ">
          <MarqueeImage
            type={slider?.type || 'default'}
            images={slider?.images || []}
            speed={slider?.speed as any}
            direction={slider?.direction as any}
            pauseOnHover={slider?.pause_on_hover as any}
          />
        </div>
      </div>
    </div>
  )

  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}

export default ImageSliderSection
