import {
  CmsMedia,
  HeadingSection,
} from '@ttplatform/core-page-builder/components'

import { Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import {
  KeyframeOptions,
  animate,
  useInView,
  useIsomorphicLayoutEffect,
} from 'framer-motion'
import { useRef } from 'react'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  container_styles: any
  heading: any
  items: any
  image_overlay: any
}
//----------------------------------------------------------------------------------
export default function StatsSection({
  styles,
  heading,
  items,
  container_styles,
  image_overlay,
}: TProps) {
  const AnimatedNumberCounter = ({
    start,
    end,
    animationOptions,
  }: {
    start: number
    end: number
    animationOptions?: KeyframeOptions
  }) => {
    const ref = useRef<HTMLSpanElement>(null)
    const isInView = useInView(ref)
    useIsomorphicLayoutEffect(() => {
      const element = ref.current
      if (!element || !isInView) return
      element.textContent = start.toString()
      // biome-ignore lint/correctness/noUnusedVariables: <explanation>
      const controls = animate(start, end, {
        duration: 1,
        ease: 'easeOut',
        ...animationOptions,
        onUpdate(value) {
          element.textContent = value.toFixed(0)
        },
      })
    }, [ref, isInView])
    return <span ref={ref} />
  }

  const renderContent = (
    <div
      className="flex rounded-3xl"
      style={{
        backgroundImage: container_styles?.image?.url
          ? `url(${RenderImageUrlStrapi({ url: container_styles?.image?.url })})`
          : 'unset',
        backgroundColor: container_styles?.color
          ? `rgb(from ${container_styles?.color} r g b / ${container_styles?.opacity})`
          : '#FFF',

        backgroundPosition: 'center center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundBlendMode: 'multiply',
        position: 'relative',
      }}
    >
      {image_overlay?.url ? (
        <div className="hidden md:block md:w-[30%] lg:w-[45%] px-10">
          <div className="relative z-10 w-full h-full">
            <CmsMedia
              media={image_overlay}
              className="w-full h-auto object-cover absolute bottom-0 z-20"
            />
          </div>
        </div>
      ) : null}
      <div
        className={cn(
          '',
          image_overlay?.url ? 'w-full md:w-[70%] lg:w-[55%]' : 'w-full',
        )}
      >
        <div className="flex flex-col gap-4 sm:gap-6 md:gap-10 p-6 md:p-10 ">
          {heading ? <HeadingSection heading={heading} /> : null}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
            {items?.map((item: any, index: number) => (
              <div
                key={index}
                className="flex flex-col gap-2 sm:gap-4 md:gap-8 p-5 sm:p-6 md:p-8"
              >
                <Typography variant={'h2'} className="text-[#FFCC00] font-bold">
                  <AnimatedNumberCounter start={0} end={item?.number ?? 0} />
                  {item?.number_suffix ? (
                    <span>&nbsp;{item.number_suffix}</span>
                  ) : null}
                </Typography>
                <Typography
                  variant={'body1'}
                  className="text-white text-justify"
                >
                  {item?.description}
                </Typography>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
