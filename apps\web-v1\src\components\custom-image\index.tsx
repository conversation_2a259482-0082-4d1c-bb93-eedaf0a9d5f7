'use client'

import { cn } from '@/utils'
import { CmsMedia } from '@ttplatform/core-page-builder/components'
import { TMediaSchema } from '@ttplatform/core-page-builder/libs'
import { AspectRatio } from '@ttplatform/ui/components'
import { ImageProps } from 'next/image'

type TProps = ImageProps & {
  ratio?: number
  className?: string
  imgClassName?: string
}

const IMG_PLACEHOLDER = '/images/placeholder.jpeg'

const CustomImage = ({
  loading = 'lazy',
  src,
  alt,
  ratio = 16 / 9,
  className,
  imgClassName,
  // ...props
}: TProps) => {
  const imgSrc = src || IMG_PLACEHOLDER

  return (
    <AspectRatio ratio={ratio} className={cn('bg-muted', className)}>
      <CmsMedia
        media={imgSrc as unknown as TMediaSchema}
        className={cn('object-cover rounded-sm', imgClassName)}
        loading={loading}
        priority
        alt={alt || ''}
      />
      {/* <Image
        src={is404 ? IMG_PLACEHOLDER : imgSrc}
        alt={alt}
        loading={loading}
        {...props}
        className={cn('object-cover rounded-sm', imgClassName)}
      /> */}
    </AspectRatio>
  )
}

export default CustomImage
