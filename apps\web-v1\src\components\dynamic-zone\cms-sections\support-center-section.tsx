'use client'

import {
  THeadingSchema,
  TPaginationSchema,
  TSectionStyleSchema,
} from '@ttplatform/core-page-builder/libs'
import { Skeleton } from '@ttplatform/ui/components'
import { EmptyContent } from '@ttplatform/ui/templates'
import { useGetSupportCenterCategories } from '~/src/libs/cms/strapi/use-support-centers'
import { useLocale } from '~/src/libs/data/use-locale'
import { BSupportCenterSection } from '~/src/modules/support-center'

type TProps = {
  heading: THeadingSchema
  pagination: TPaginationSchema
  styles: TSectionStyleSchema
}

export default function SupportCenterSection({
  heading,
  pagination,
  styles,
}: TProps) {
  const locale = useLocale()

  const _mockLimit = 100

  const { data, isLoading, error } = useGetSupportCenterCategories({
    locale,
    sort: 'rank:asc,publishedAt:desc',
    pagination: {
      page: 1,
      pageSize: _mockLimit,
    },
    populate: '*',
  })

  const notFound = !isLoading && (!data?.data?.length || error)

  if (notFound) {
    return (
      <EmptyContent
        title="Nội dung đang được cập nhật"
        className="bg-gray-50 mt-10 italic"
      />
    )
  }

  if (isLoading) {
    return <SupportCenterSkeleton />
  }

  return (
    <BSupportCenterSection
      categories={data?.data || []}
      heading={heading}
      pagination={pagination}
      styles={styles}
    />
  )
}

const SupportCenterSkeleton = () => {
  return (
    <div className="flex w-full max-w-screen-xl mx-auto gap-4 py-4">
      <div className="w-full md:w-1/4">
        <Skeleton className="w-full h-[60dvh] rounded-md" />
      </div>
      <div className="w-full md:w-3/4 flex flex-wrap h-[60dvh]">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full">
          {Array.from({ length: 12 }).map((_, index) => (
            <Skeleton key={index} className="rounded-md" />
          ))}
        </div>
      </div>
    </div>
  )
}
