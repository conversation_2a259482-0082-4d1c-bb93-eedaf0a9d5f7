{"kind": "collectionType", "collectionName": "part_categories", "info": {"singularName": "part-category", "pluralName": "part-categories", "displayName": "Product-Part Categories"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "targetField": "title"}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "image": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images"]}, "parts": {"type": "relation", "relation": "oneToMany", "target": "api::part.part", "mappedBy": "category"}, "dynamic_zone": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["product-parts.parts-by-category", "dynamic-zone.page-hero-section", "dynamic-zone.icon-box-list-section"]}}}