import { Typography } from '@ttplatform/ui/components'
import Image from 'next/image'
import { cn } from '~/src/utils'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
  icon_box_list: any
}
//----------------------------------------------------------------------------------
export default function IconBoxListSection({
  styles,
  heading,
  icon_box_list,
}: TProps) {
  const {
    items,
    icon_position,
    text_align,
    item_border_radius,
    desktop_item_count,
    tablet_item_count,
    mobile_item_count,
    gap,
    item_background,
  } = icon_box_list || {}

  const renderContent = (
    <div className="rounded-3xl flex flex-col gap-y-5 sm:gap-y-8 md:gap-y-10">
      {heading?.heading?.text ? <HeadingSection heading={heading} /> : null}
      <div
        className={cn(
          ' grid',
          `grid-cols-${mobile_item_count} md:grid-cols-${tablet_item_count} lg:grid-cols-${desktop_item_count}`,
        )}
        style={{
          gap: gap || '0px',
        }}
      >
        {items?.map((item: any, idx: number) => (
          <IconBoxItem
            key={idx}
            item={item}
            icon_position={icon_position}
            text_align={text_align}
            item_background={item_background}
            item_border_radius={item_border_radius}
          />
        ))}
      </div>
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
//----------------------------------------------------------------------------------
function IconBoxItem({
  item,
  icon_position,
  text_align,
  item_background,
  item_border_radius,
}: {
  item: any
  icon_position: any
  text_align: any
  item_background: any
  item_border_radius: any
}) {
  const renderIconPosition = (text: string) => {
    switch (text) {
      case 'top':
        return 'flex-col items-start'
      case 'center':
        return 'flex-col items-center'
      case 'left':
        return 'flex-row items-start'
    }
  }

  const renderIcon = (
    <Image
      src={RenderImageUrlStrapi({ url: item?.icon?.url })}
      alt="why choose us icon"
      width={48}
      height={48}
      className="w-8 min-w-8 sm:w-10 sm:min-w-10 md:w-12 md:min-w-12 aspect-square"
    />
  )
  return (
    <div
      className={cn(
        'flex gap-4 md:gap-6 py-6 md:py-10 lg:py-16 px-4 md:px-6 lg:px-10',
        renderIconPosition(icon_position),
      )}
      style={{
        backgroundImage: item_background?.image?.url
          ? `url(${RenderImageUrlStrapi({ url: item_background?.image?.url })})`
          : 'unset',
        backgroundColor: item_background?.color
          ? `rgb(from ${item_background?.color} r g b / ${item_background?.opacity})`
          : 'transparent',

        backgroundPosition: 'center center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundBlendMode: 'multiply',
        position: 'relative',
        borderRadius: item_border_radius || '0px',

        boxShadow: item_background?.color
          ? '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)'
          : 'unset',
      }}
    >
      {renderIcon}
      <div className={cn('flex flex-col gap-4', `text-${text_align}`)}>
        <Typography variant={'h5'} className="text-gray-800 font-semibold">
          {item?.title}
        </Typography>
        <Typography variant={'body2'} className="text-gray-800 font-normal">
          {item?.sub_title}
        </Typography>
      </div>
    </div>
  )
}
