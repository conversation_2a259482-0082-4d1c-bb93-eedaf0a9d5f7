import { Metadata } from 'next'

import Client<PERSON>lugHandler from '~/src/app/(locale)/client-slug-handler'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
// import ServerSiteHandler from '../../server-site-handler'

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pageHome,
    params: {
      filters: {
        locale: locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = pageData?.seo
  const metadata = generateMetadataObject(seo, pageData?.title)
  return metadata
}

export default async function HomePage() {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pageHome,
    params: {
      filters: { locale: locale },
    },
    spreadData: true,
  })

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = ''
      return acc
    },
    { [locale]: '' },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />
      {/* <ServerSiteHandler /> */}
      <PageContent pageData={pageData} />
    </>
  )
}
