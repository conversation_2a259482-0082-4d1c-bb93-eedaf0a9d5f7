'use client'

import {
  CmsMedia,
  MainButton,
  getBackgroundImageUrl,
} from '@ttplatform/core-page-builder/components'
import {
  TProductIntroFields,
  renderFontWeight,
  renderMaxWidth,
  renderTextAlign,
} from '@ttplatform/core-page-builder/libs'
import { Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { motion } from 'framer-motion'

export const BProductIntro: React.FC<TProductIntroFields> = ({
  styles,
  items,
}) => {
  const { background, content_max_width } = styles || {}

  return (
    <section
      style={{
        ...(background && {
          backgroundImage: background?.image?.url
            ? `url(${getBackgroundImageUrl(background)})`
            : 'unset',
          backgroundColor: background?.color
            ? `rgb(from ${background?.color} r g b / ${background?.opacity})`
            : '#FFF',
          backgroundPosition: 'center center',
          backgroundRepeat: background?.repeat || 'no-repeat',
          backgroundSize: background?.size || 'cover',
        }),
        position: 'relative',
      }}
      className={cn('h-full', renderMaxWidth(content_max_width || ''))}
    >
      <div
        className={cn(
          'w-full grid gap-2 p-2 grid-cols-1 md:grid-cols-2 xl:grid-cols-3 auto-rows-fr',
        )}
      >
        {items?.map((item, index) => {
          return (
            <GridItem key={index} item={item} />
            // <motion.div
            //   key={index}
            //   className={cn(
            //     'group relative overflow-hidden rounded-lg h-[344px]',
            //     `${item.hover_content?.sub_heading ? 'xs:h-auto ' : ''}`,
            //   )}
            //   whileHover="hover"
            //   initial="initial"
            // >
            //   <CmsMedia
            //     media={item.background?.image}
            //     className={cn(
            //       'object-cover transition-transform duration-300 w-full h-full group-hover:scale-105',
            //     )}
            //     fill
            //   />
            //   <div className="absolute inset-0">
            //     <div
            //       style={{
            //         backgroundColor: item?.overlay_color || 'transparent',
            //         opacity: item?.opacity || 1,
            //       }}
            //       className="w-full h-full"
            //     ></div>
            //   </div>
            //   <div className={cn('absolute inset-0 flex items-end')}>
            //     {item.hover_content?.sub_heading ? (
            //       <div className="mt-8 flex h-full flex-col items-center justify-center text-center px-10">
            //         <Typography
            //           weight={renderFontWeight(
            //             item?.hover_content?.heading?.styles
            //               ?.font_weight as any,
            //           )}
            //           className={cn(
            //             'uppercase',
            //             renderTextAlign(
            //               item?.hover_content?.heading?.styles
            //                 ?.text_align as any,
            //             ),
            //           )}
            //           color={item?.hover_content?.heading?.styles?.color as any}
            //           variant={
            //             item?.hover_content?.heading?.styles?.variant as any
            //           }
            //         >
            //           {item?.hover_content?.heading?.text}
            //         </Typography>
            //         {item?.hover_content?.sub_heading && (
            //           <Typography
            //             color={
            //               item?.hover_content?.sub_heading?.styles?.color as any
            //             }
            //             variant={
            //               item?.hover_content?.sub_heading?.styles
            //                 ?.variant as any
            //             }
            //             weight={renderFontWeight(
            //               item?.hover_content?.sub_heading?.styles
            //                 ?.font_weight as any,
            //             )}
            //             className={cn(
            //               renderTextAlign(
            //                 item?.hover_content?.sub_heading?.styles
            //                   ?.text_align as any,
            //               ),
            //             )}
            //           >
            //             {item?.hover_content?.sub_heading?.text}
            //           </Typography>
            //         )}
            //         <div className="flex flex-col gap-2">
            //           {item?.hover_content?.buttons?.map((button, index) => (
            //             <MainButton
            //               key={index}
            //               label={button?.text || ''}
            //               variant={button?.variant as any}
            //               // active={item?.hover_content?.buttons?.[0]?.active}
            //               className={cn('mt-4')}
            //               url={button?.URL || ''}
            //             />
            //           ))}
            //         </div>
            //       </div>
            //     ) : (
            //       <div className="w-full px-8">
            //         <Typography
            //           weight={renderFontWeight(
            //             item?.hover_content?.heading?.styles
            //               ?.font_weight as any,
            //           )}
            //           className={cn(
            //             'uppercase',
            //             renderTextAlign(
            //               item?.hover_content?.heading?.styles
            //                 ?.text_align as any,
            //             ),
            //           )}
            //           color={item?.hover_content?.heading?.styles?.color as any}
            //           variant={
            //             item?.hover_content?.heading?.styles?.variant as any
            //           }
            //         >
            //           {item?.hover_content?.heading?.text}
            //         </Typography>
            //         <>
            //           <div className="block md:hidden mt-8">
            //             <MainButton
            //               label={item?.hover_content?.buttons?.[0]?.text || ''}
            //               variant={
            //                 (item?.hover_content?.buttons?.[0]
            //                   ?.variant as any) || 'primary'
            //               }
            //               className={cn('mb-5')}
            //             />
            //           </div>

            //           <motion.div
            //             initial={{ height: 0, opacity: 0 }}
            //             transition={{ duration: 0.3, ease: 'easeOut' }}
            //             className={cn(
            //               'mt-8 md:group-hover:mt-6 overflow-hidden md:group-hover:h-auto md:group-hover:opacity-100',
            //               'hidden md:block',
            //             )}
            //             animate={{
            //               height: 0,
            //               opacity: 0,
            //             }}
            //             variants={{
            //               hover: { height: 'auto', opacity: 1 },
            //             }}
            //           >
            //             <MainButton
            //               label={item?.hover_content?.buttons?.[0]?.text || ''}
            //               variant={
            //                 (item?.hover_content?.buttons?.[0]
            //                   ?.variant as any) || 'primary'
            //               }
            //               className={cn('mb-5')}
            //               url={item?.hover_content?.buttons?.[0]?.URL || ''}
            //             />
            //           </motion.div>
            //         </>
            //       </div>
            //     )}
            //   </div>
            // </motion.div>
          )
        })}
      </div>
    </section>
  )
}

const HoverOverlayCardSection = ({ items, styles }: TProductIntroFields) => {
  // const props = mapProductIntroSchema(productIntroSchema)
  return <BProductIntro items={items} styles={styles} />
}

export default HoverOverlayCardSection

function GridItem({ item }: { item: any }) {
  return (
    <motion.div
      className="p-10 flex flex-col group relative overflow-hidden rounded-lg "
      whileHover="hover"
      initial="initial"
    >
      <CmsMedia
        media={item.background?.image}
        className={cn(
          'object-cover absolute inset-0 transition-transform duration-300 w-full h-full group-hover:scale-105',
        )}
        fill
      />
      <div className="absolute inset-0">
        <div
          style={{
            backgroundColor: item?.overlay_color || 'transparent',
            opacity: item?.opacity || 1,
          }}
          className="w-full h-full"
        ></div>
      </div>
      {item.hover_content?.sub_heading ? (
        <div className="relative z-50 flex h-full flex-col gap-4 items-center justify-center text-center ">
          <Typography
            weight={renderFontWeight(
              item?.hover_content?.heading?.styles?.font_weight as any,
            )}
            className={cn(
              'uppercase',
              renderTextAlign(
                item?.hover_content?.heading?.styles?.text_align as any,
              ),
            )}
            color={item?.hover_content?.heading?.styles?.color as any}
            variant={item?.hover_content?.heading?.styles?.variant as any}
          >
            {item?.hover_content?.heading?.text}
          </Typography>
          {item?.hover_content?.sub_heading && (
            <Typography
              color={item?.hover_content?.sub_heading?.styles?.color as any}
              variant={item?.hover_content?.sub_heading?.styles?.variant as any}
              weight={renderFontWeight(
                item?.hover_content?.sub_heading?.styles?.font_weight as any,
              )}
              className={cn(
                renderTextAlign(
                  item?.hover_content?.sub_heading?.styles?.text_align as any,
                ),
              )}
            >
              {item?.hover_content?.sub_heading?.text}
            </Typography>
          )}
          <div className="flex flex-col gap-2">
            {item?.hover_content?.buttons?.map((button: any, index: number) => (
              <MainButton
                key={index}
                label={button?.text || ''}
                variant={button?.variant as any}
                // active={item?.hover_content?.buttons?.[0]?.active}
                className={cn('mt-4')}
                url={button?.URL || ''}
              />
            ))}
          </div>
        </div>
      ) : (
        <div className="w-full h-full px-8 flex flex-col justify-end relative z-50">
          <Typography
            weight={renderFontWeight(
              item?.hover_content?.heading?.styles?.font_weight as any,
            )}
            className={cn(
              'uppercase',
              renderTextAlign(
                item?.hover_content?.heading?.styles?.text_align as any,
              ),
            )}
            color={item?.hover_content?.heading?.styles?.color as any}
            variant={item?.hover_content?.heading?.styles?.variant as any}
          >
            {item?.hover_content?.heading?.text}
          </Typography>
          <>
            <div className="block md:hidden mt-8">
              <MainButton
                label={item?.hover_content?.buttons?.[0]?.text || ''}
                variant={
                  (item?.hover_content?.buttons?.[0]?.variant as any) ||
                  'primary'
                }
                className={cn('mb-5')}
              />
            </div>

            <motion.div
              initial={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: 'easeOut' }}
              className={cn(
                'mt-8 md:group-hover:mt-6 overflow-hidden md:group-hover:h-auto md:group-hover:opacity-100',
                'hidden md:block',
              )}
              animate={{
                height: 0,
                opacity: 0,
              }}
              variants={{
                hover: { height: 'auto', opacity: 1 },
              }}
            >
              <MainButton
                label={item?.hover_content?.buttons?.[0]?.text || ''}
                variant={
                  (item?.hover_content?.buttons?.[0]?.variant as any) ||
                  'primary'
                }
                className={cn('mb-5')}
                url={item?.hover_content?.buttons?.[0]?.URL || ''}
              />
            </motion.div>
          </>
        </div>
      )}
    </motion.div>
  )
}
