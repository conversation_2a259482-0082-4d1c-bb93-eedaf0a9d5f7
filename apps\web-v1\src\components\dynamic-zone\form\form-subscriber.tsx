'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { t } from '@lingui/core/macro'
import { MainButton } from '@ttplatform/core-page-builder/components'
import {
  THeadingSchema,
  TSectionStyleSchema,
  TSocialSchema,
  getStrapiImageUrl,
} from '@ttplatform/core-page-builder/libs'
import { Input, Typography } from '@ttplatform/ui/components'
import { motion, useScroll, useSpring, useTransform } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { useRef } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { useMediaQuery } from 'usehooks-ts'
import * as z from 'zod'
import { submitFormSubscription } from '~/src/libs/form/submit-form'
import SocialIcon from '../../socials/social-icon'

// Helper function to get background image URL - simple and clean like BTestimonials
const getBackgroundImageUrl = (background?: any): string => {
  if (
    !background?.image ||
    !Array.isArray(background.image) ||
    background.image.length === 0
  ) {
    return '/images/contact-cover.png' // fallback
  }

  const image = background.image[0]
  return getStrapiImageUrl(image, 'large') || '/images/contact-cover.png'
}

// Define the validation schema
const formSchema = z.object({
  name: z.string().min(1, { message: t`Name is required` }),
  email: z
    .string()
    .min(1, { message: t`Email is required` })
    .email({ message: t`Invalid email format` }),
})

type FormValues = z.infer<typeof formSchema>

type TProps = {
  heading: THeadingSchema
  sub_heading: THeadingSchema
  styles: TSectionStyleSchema
  locale: string
  social_text: string
  social_items: TSocialSchema[]
}

const FormSubscriber = ({
  heading,
  styles,
  social_text,
  social_items,
}: TProps) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const isMobile = useMediaQuery('(max-width: 767px)')

  const {
    reset,
    register,
    handleSubmit,
    formState: { errors },
    trigger,
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      name: '',
      email: '',
    },
  })

  // Form submission handler
  const onSubmit = async (data: FormValues) => {
    try {
      const payload = {
        name: data.name,
        email: data.email,
      }

      const href = (window && window.location.href) || ''

      await submitFormSubscription(payload, href)

      reset()

      toast.success(t`Cảm ơn bạn đã đăng ký!`)
    } catch (error: any) {
      console.error('Form submission error:', error)
      const errorMessage =
        error?.message || t`Có lỗi xảy ra. Vui lòng thử lại sau.`
      toast.error(errorMessage)
    }
  }

  const onError = (errors: Record<string, { message?: string }>) => {
    const errorMessages: { [key: string]: string } = {}
    Object.keys(errors).forEach((field) => {
      const message = errors[field]?.message || 'Invalid field'
      const errorMsg = typeof message === 'string' ? message : 'Invalid field'

      errorMessages[field] = errorMsg
      trigger(field as keyof FormValues)
    })
  }

  // Animation settings (called unconditionally)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start 100vh', 'end 50vh'],
  })

  const widthProgress = useTransform(
    scrollYProgress,
    [0, 0.3],
    isMobile ? ['100%', '100%'] : ['10%', '100%'],
  )
  const heightProgress = useTransform(
    scrollYProgress,
    [0, 0.5],
    isMobile ? ['100%', '100%'] : ['50%', '100%'],
  )
  const borderRadiusProgress = useTransform(
    scrollYProgress,
    [0, 0.5],
    isMobile ? ['0px', '0px'] : ['400px', '0px'],
  )

  const width = useSpring(widthProgress, { stiffness: 100, damping: 30 })
  const height = useSpring(heightProgress, { stiffness: 100, damping: 30 })
  const borderRadius = useSpring(borderRadiusProgress, {
    stiffness: 100,
    damping: 30,
  })

  // Get background image URL and opacity from Strapi or fallback
  const backgroundImageUrl = getBackgroundImageUrl(styles?.background)
  const imageOpacity = styles?.background?.opacity ?? 1

  const renderForm = (
    <div className="flex flex-col items-center gap-10 max-w-[960px] w-full">
      <div className="text-white text-center flex flex-col items-center max-w-[800px] mx-auto">
        {heading?.heading?.text && (
          <Typography
            variant="h2"
            className="mb-4 uppercase"
            style={{ textShadow: '1px 1px 2px rgba(0, 0, 0, 0.5)' }}
          >
            {heading?.heading?.text}
          </Typography>
        )}

        {heading?.sub_heading?.text && (
          <Typography
            variant="body1"
            style={{ textShadow: '1px 1px 2px rgba(0, 0, 0, 0.5)' }}
          >
            {heading?.sub_heading?.text}
          </Typography>
        )}
      </div>
      <form
        onSubmit={handleSubmit(onSubmit, onError)}
        className="w-full max-w-[860px] text-left flex flex-col gap-10"
        noValidate
      >
        <div className="flex flex-col md:flex-row items-start space-y-4 md:space-y-0 md:space-x-6 w">
          <div className="w-full md:w-1/2">
            <Typography variant="caption" className="text-gray-200">
              Name
            </Typography>
            <Input
              {...register('name')}
              placeholder="Jane Smith"
              className="mt-2 w-full bg-white text-black"
            />
            {errors.name && (
              <p className="text-red-400 text-left text-sm mt-1">
                {errors.name.message}
              </p>
            )}
          </div>
          <div className="w-full md:w-1/2">
            <Typography variant="caption" className="text-gray-200">
              Email
            </Typography>
            <Input
              {...register('email')}
              placeholder="<EMAIL>"
              type="email"
              className="mt-2 w-full bg-white text-black"
            />
            {errors.email && (
              <p className="text-red-400 text-left text-sm mt-1">
                {errors.email.message}
              </p>
            )}
          </div>
        </div>

        <div className="flex justify-center">
          {heading?.buttons && heading?.buttons?.length > 0 ? (
            heading?.buttons?.map((button, index: number) => {
              return (
                <MainButton
                  key={index}
                  variant="primary"
                  type="submit"
                  label={button.text || 'GET STARTED'}
                />
              )
            })
          ) : (
            <MainButton variant="primary" type="submit" />
          )}
        </div>
      </form>
    </div>
  )

  const renderSocials = (
    <div
      className="w-full rounded-[16px] px-4 md:px-10 lg:px-20 py-4 md:py-8 lg:py-16 flex flex-col md:flex-row items-center justify-between mx-auto min-h-[120px] md:min-h-[140px] lg:min-h-[162px] max-w-[860px]"
      style={{
        background:
          'linear-gradient(90deg, rgba(255,204,0,1) 0%, rgba(255,211,38,1) 70%, rgba(255,222,89,1) 100%)',
      }}
    >
      <Typography variant="h3" className="text-black mb-4 md:mb-0">
        {social_text || 'Kết nối với Phú Thái Cat'}
      </Typography>
      <div className="flex space-x-4 lg:space-x-8 xl:space-x-10 items-center">
        {social_items?.map((item, idx) => (
          <Link
            key={idx}
            href={item.url || '#'}
            className="flex items-center gap-2"
          >
            <SocialIcon
              item={item}
              className="w-6 h-6 sm:w-8 sm:h-8 transition-all duration-300 hover:scale-105"
            />
          </Link>
        ))}
      </div>
    </div>
  )

  const content = (
    <div className="w-full h-full relative">
      <Image
        src={backgroundImageUrl}
        alt="Background image"
        quality={85}
        className="object-cover object-center w-full h-full"
        style={{ opacity: imageOpacity }}
        priority
        width={800}
        height={600}
      />
      <div
        className="absolute inset-0"
        style={{
          backgroundImage:
            'linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7))',
        }}
      />
      <div className="absolute inset-0 flex flex-col items-center justify-center px-4">
        <div className="flex flex-col items-center w-full  lg:max-w-5xl gap-8 md:gap-16 lg:gap-[120px] 3xl:gap-[200px]">
          {renderForm}

          {renderSocials}
        </div>
      </div>
    </div>
  )

  return (
    <div className="bg-[#fffbf0] w-full">
      <div
        ref={containerRef}
        className="relative  h-screen min-h-screen flex items-center justify-center"
      >
        <motion.div
          style={
            isMobile
              ? { width: '100%', height: '100%', borderRadius: '0px' }
              : { width, height, borderRadius }
          }
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="w-full h-full mx-auto overflow-hidden"
        >
          {content}
        </motion.div>
      </div>
    </div>
  )
}

export default FormSubscriber
