{"collectionName": "components_dynamic_zone_executive_team_sections", "info": {"displayName": "Executive_Team_Section"}, "options": {}, "attributes": {"adminLabel": {"type": "string", "private": true}, "styles": {"type": "component", "component": "styles.section-styles", "repeatable": false}, "heading": {"type": "component", "component": "elementals.heading", "repeatable": false}, "items": {"type": "relation", "relation": "oneToMany", "target": "api::team-member.team-member"}}, "config": {}}