'use client'

// import { useParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react'
import fetchContentTypeClient from '~/src/libs/cms/strapi/fetchContentTypeClient'
import { apiRoute } from '~/src/libs/constants/cms'
import { useLocale } from '~/src/libs/data/use-locale'
import Card<PERSON><PERSON><PERSON>oom from '../card/card-hover-zoom'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
}
//----------------------------------------------------------------------------------
export default function ProductPartCategories({ styles, heading }: TProps) {
  const locale = useLocale()

  const [categoriesData, setCategoriesData] = useState<any>(null)

  const fetchPartCategoriesData = useCallback(async () => {
    const partCategories = await fetchContentTypeClient({
      contentType: apiRoute.partCategories,
      params: {
        filters: {
          // rank: 1,
          locale,
        },
      },
    })
    setCategoriesData(partCategories?.data)
  }, [locale])

  useEffect(() => {
    fetchPartCategoriesData()
  }, [fetchPartCategoriesData])

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <div
        className="flex flex-col p-5 sm:p-10 lg:p-20 gap-8 md:gap-10 lg:gap-16 rounded-2xl bg-[#FAFAFA]"
        style={{
          boxShadow:
            '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
        }}
      >
        {categoriesData?.map((category: any, idx: number) => (
          <CardHoverZoom
            key={idx}
            name={category?.title}
            description={category?.description}
            image={category?.image}
            href={`/parts/${category?.slug}`}
          />
        ))}
      </div>
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
