'use client'

import { useClientFetch } from './use-client-fetch'

export interface ProductSpecificationValue {
  id: number
  documentId: string
  value: string
  value_in_us?: string
  product_specification: {
    id: number
    documentId: string
    name: string
    unit?: string
    unit_in_us?: string
  }
  product_model: {
    id: number
    documentId: string
    name: string
  }
}

export interface ProductSpecificationResponse {
  data: ProductSpecificationValue[]
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

export function useProductSpecifications(productSlug?: string) {
  const filters = productSlug
    ? {
        product_model: {
          product: {
            slug: {
              $eq: productSlug,
            },
          },
        },
      }
    : {}

  const { data, error, isLoading } = useClientFetch<ProductSpecificationResponse>(
    '/api/product-specification-values',
    {
      populate: ['product_specification', 'product_model'],
      filters,
      pagination: {
        pageSize: 100, // Get more data
      },
    }
  )

  return {
    specifications: data?.data || [],
    isLoading,
    error,
  }
}
