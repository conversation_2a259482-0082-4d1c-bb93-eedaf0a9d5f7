'use client'

import { useParams } from 'next/navigation'
import { useCallback, useEffect, useState } from 'react'
import fetchContentTypeClient from '~/src/libs/cms/strapi/fetchContentTypeClient'
import { apiRoute } from '~/src/libs/constants/cms'
import { useLocale } from '~/src/libs/data/use-locale'
import MediaGallery from '../media-gallery'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
}
//----------------------------------------------------------------------------------

export default function SolutionMediaGallery({ styles, heading }: TProps) {
  const { segments } = useParams()
  const locale = useLocale()

  const [industryData, setIndustryData] = useState<any>(null)

  const fetchIndustryData = useCallback(async () => {
    const data = await fetchContentTypeClient({
      contentType: apiRoute.industrialSolutions,
      params: {
        filters: {
          slug: segments?.[1],
          locale,
        },
      },
    })
    setIndustryData(data?.data?.[0])
  }, [locale, segments])

  useEffect(() => {
    fetchIndustryData()
  }, [fetchIndustryData])

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <MediaGallery
        galleryData={industryData?.media_gallery}
        content_max_width={styles?.content_max_width}
      />
    </div>
  )

  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
