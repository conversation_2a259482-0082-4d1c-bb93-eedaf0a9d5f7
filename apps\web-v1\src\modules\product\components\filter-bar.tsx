import { Trans } from '@lingui/react/macro'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Label,
  Typography,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useProductTypes } from '../../../libs/cms/strapi/use-product-filters'
import { useProductSpecifications } from '../../../libs/cms/strapi/use-product-specifications'
import type { DynamicFilterSpec } from '../../../libs/types/product'
import { CustomCheckbox } from './custom-checkbox'

//---------------------------------------------------------------------------------
type FilterBarProps = {
  filters: any
  onFilters: (name: string, value: any) => void
  productSlug?: string // Add product slug to get product-types for this specific product
  onSpecificationFilters?: (filters: Record<string, string[]>) => void // Callback for specification filters
}

//---------------------------------------------------------------------------------
export default function FilterBar({
  filters,
  onFilters,
  productSlug,
  onSpecificationFilters,
}: FilterBarProps) {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedSpecificationOptions, setSelectedSpecificationOptions] =
    useState<Record<string, string[]>>({})
  const [showAllProducts, setShowAllProducts] = useState(true) // Default to show all products
  const [unitSystem, setUnitSystem] = useState<'metric' | 'us'>('metric') // Default to metric

  // Fetch dynamic data
  const {
    categoryOptions,
    isLoading: categoriesLoading,
    error: categoriesError,
  } = useProductTypes(productSlug)

  const {
    specifications: productSpecifications,
    isLoading: specificationsLoading,
  } = useProductSpecifications(productSlug)

  // Transform real specification data into filter options
  const realSpecifications = useMemo(() => {
    if (!productSpecifications.length) return []

    // Group by specification name
    const specGroups = productSpecifications.reduce(
      (acc, spec) => {
        const specName = spec.product_specification.name
        const specId = spec.product_specification.documentId

        if (!acc[specId]) {
          acc[specId] = {
            id: specId,
            name: specName,
            slug: specName.toLowerCase().replace(/\s+/g, '-'),
            type: 'options' as const,
            unit: spec.product_specification.unit,
            unit_in_us: spec.product_specification.unit_in_us,
            isLoading: false,
            options: [],
          }
        }

        // Add unique values as options
        const metricValue = spec.value
        const usValue = spec.value_in_us || spec.value

        const existingOption = acc[specId].options.find(
          (opt: any) => opt.value === metricValue || opt.value === usValue,
        )

        if (!existingOption) {
          acc[specId].options.push({
            id: `${specId}-${metricValue}`,
            label: metricValue,
            value: metricValue,
            usValue: usValue,
            count: 1,
          })
        }

        return acc
      },
      {} as Record<string, any>,
    )

    return Object.values(specGroups)
  }, [productSpecifications])

  // Notify parent about specification filter changes
  useEffect(() => {
    if (onSpecificationFilters) {
      onSpecificationFilters(selectedSpecificationOptions)
    }
  }, [selectedSpecificationOptions, onSpecificationFilters])

  const filteredCategoryOptions = useMemo(() => {
    return categoryOptions.filter((option: any) => {
      return option.count > 0 || option.hasProduct
    })
  }, [categoryOptions])

  // Sync selectedCategories with filters prop
  useEffect(() => {
    if (filters?.category && Array.isArray(filters.category)) {
      const uniqueCategories = [...new Set(filters.category)] as string[]
      setSelectedCategories(uniqueCategories)
    } else if (filters?.category && typeof filters.category === 'string') {
      setSelectedCategories([filters.category])
    } else {
      setSelectedCategories([])
    }
  }, [filters?.category])

  const handleShowAllProducts = useCallback(
    (checked: boolean) => {
      setShowAllProducts(checked)
      if (checked) {
        // When "Show all" is checked, clear all category filters
        setSelectedCategories([])
        onFilters('category', [])
      }
    },
    [onFilters],
  )

  const handleFilterCategory = useCallback(
    (slug: string, checked: boolean) => {
      if (checked) {
        setShowAllProducts(false)
      }

      setSelectedCategories((prev) => {
        const newSelection = checked
          ? [...prev, slug]
          : prev.filter((item) => item !== slug)

        if (newSelection.length === 0) {
          setShowAllProducts(true)
        }

        onFilters('category', newSelection)
        return newSelection
      })
    },
    [onFilters],
  )

  const handleSpecificationOptionFilter = useCallback(
    (specSlug: string, optionSlug: string, checked: boolean) => {
      setSelectedSpecificationOptions((prev) => {
        const currentOptions = prev[specSlug] || []
        const newSelection = checked
          ? [...currentOptions, optionSlug]
          : currentOptions.filter((item) => item !== optionSlug)

        const updatedState = {
          ...prev,
          [specSlug]: newSelection,
        }

        onFilters(specSlug, newSelection)
        return updatedState
      })
    },
    [onFilters],
  )

  // Render category filter with improved UI and functionality
  const renderFilterCategory = (
    <div className="flex flex-col gap-4 mt-2">
      <Typography
        variant="h6"
        className="text-gray-800 uppercase font-semibold"
      >
        <Trans>Category</Trans>
      </Typography>
      <div className="flex flex-col gap-3">
        {categoriesLoading ? (
          <div className="flex items-center gap-2 text-gray-500">
            <div className="w-4 h-4 border-2 border-gray-300 border-t-primary rounded-full animate-spin"></div>
            <span>Loading categories...</span>
          </div>
        ) : categoriesError ? (
          <div className="text-red-500 text-sm">
            <Trans>Error loading categories. Please try again.</Trans>
          </div>
        ) : filteredCategoryOptions.length === 0 ? (
          <div className="text-gray-500 text-sm">No categories available</div>
        ) : (
          <>
            {/* Show All Products option - same format as other categories */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CustomCheckbox
                  id="show-all-products"
                  checked={showAllProducts}
                  onCheckedChange={handleShowAllProducts}
                  className="cursor-pointer"
                />
                <Label
                  className={cn(
                    'cursor-pointer flex-1 text-sm transition-colors select-none',
                    showAllProducts
                      ? 'font-medium text-gray-900'
                      : 'font-normal text-gray-700 group-hover:text-gray-900',
                  )}
                  htmlFor="show-all-products"
                >
                  <Trans>All Products</Trans>
                </Label>
              </div>
            </div>

            {/* Category options */}
            <div
              className="space-y-2 max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
              role="group"
              aria-label="Product categories"
            >
              {filteredCategoryOptions.map((category) => (
                <div key={category.id} className="flex items-center">
                  <div className="flex items-center space-x-3">
                    <CustomCheckbox
                      id={category.value as string}
                      checked={selectedCategories.includes(
                        category.value as string,
                      )}
                      onCheckedChange={(checked) =>
                        handleFilterCategory(
                          category.value as string,
                          checked as boolean,
                        )
                      }
                      className="cursor-pointer"
                    />
                    <Label
                      className={cn(
                        'cursor-pointer flex-1 text-sm transition-colors select-none',
                        selectedCategories.includes(category.value as string)
                          ? 'font-medium text-gray-900'
                          : 'font-normal text-gray-700 group-hover:text-gray-900',
                      )}
                      htmlFor={category.value as string}
                    >
                      {category.label}
                    </Label>
                  </div>

                  {/* Product count badge */}
                  {category.count !== undefined && category.count > 0 && (
                    <span
                      className={cn(
                        'text-sm ml-1 flex-shrink-0',
                        selectedCategories.includes(category.value as string)
                          ? 'font-medium text-gray-900'
                          : 'font-normal text-gray-700 group-hover:text-gray-900',
                      )}
                    >
                      ({category.count})
                    </span>
                  )}
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  )

  // Render dynamic specification filter
  const renderSpecificationFilter = (spec: DynamicFilterSpec) => {
    if (spec.isLoading) {
      return (
        <Accordion
          key={spec.id}
          type="single"
          collapsible
          defaultValue="item-1"
        >
          <AccordionItem value="item-1">
            <AccordionTrigger className="py-0 hover:no-underline cursor-pointer">
              <Typography variant="body1" className="text-gray-700 font-medium">
                <Trans>{spec.name}</Trans>
              </Typography>
            </AccordionTrigger>
            <AccordionContent className="!py-0">
              <div className="text-gray-500">Loading...</div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )
    }

    if (spec.type === 'options') {
      const selectedOptions = selectedSpecificationOptions[spec.slug] || []
      return (
        <Accordion
          key={spec.id}
          type="single"
          collapsible
          defaultValue="item-1"
        >
          <AccordionItem value="item-1">
            <AccordionTrigger className="py-0 hover:no-underline cursor-pointer">
              <Typography variant="body1" className="text-gray-700 font-medium">
                <Trans>{spec.name}</Trans>
                {spec.unit && (
                  <span className="text-gray-500 font-normal ml-1">
                    (
                    {unitSystem === 'metric'
                      ? spec.unit
                      : spec.unit_in_us || spec.unit}
                    )
                  </span>
                )}
              </Typography>
            </AccordionTrigger>
            <AccordionContent className="!py-0">
              <div className="flex flex-col gap-4 mt-4">
                {spec.options.map((option) => (
                  <div
                    key={option.id}
                    className="flex items-center space-x-2 cursor-pointer"
                  >
                    <CustomCheckbox
                      id={`${spec.slug}-${option.value}`}
                      checked={selectedOptions.includes(
                        option.value.toString(),
                      )}
                      onCheckedChange={(checked) =>
                        handleSpecificationOptionFilter(
                          spec.slug,
                          option.value.toString(),
                          checked as boolean,
                        )
                      }
                      className="cursor-pointer"
                    />
                    <Typography variant="body2">
                      <Label
                        className={cn(
                          'cursor-pointer',
                          selectedOptions.includes(option.value.toString())
                            ? 'font-medium'
                            : 'font-normal',
                        )}
                        htmlFor={`${spec.slug}-${option.value}`}
                      >
                        {unitSystem === 'metric'
                          ? option.label
                          : option.usValue || option.label}
                      </Label>
                    </Typography>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )
    }

    return null
  }

  return (
    <div className="sticky top-4 h-[calc(100vh-2rem)]">
      <div
        className="flex flex-col h-full border border-gray-200/20 rounded-lg bg-white shadow-sm overflow-hidden"
        style={{
          overscrollBehavior: 'contain',
        }}
      >
        <div className="p-6 pb-4 flex-shrink-0">
          <Typography
            variant="h5"
            className="text-gray-800 uppercase font-bold"
          >
            <Trans>FILTER PRODUCTS</Trans>
          </Typography>
        </div>
        <div
          className="flex-1 overflow-y-auto pr-0 pl-6 pb-6 custom-scrollbar"
          style={{
            overscrollBehavior: 'contain',
          }}
          onWheel={(e) => {
            e.stopPropagation()
          }}
        >
          <div className="flex flex-col gap-4 2xl:gap-6 pr-3">
            {renderFilterCategory}

            <div className="flex flex-col">
              <Typography
                variant="h6"
                className="text-gray-800 uppercase font-semibold"
              >
                <Trans>Specifications</Trans>
              </Typography>

              {/* Unit Toggle */}
              <div className="flex items-center justify-between">
                <div>Units of measure</div>
                <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1">
                  <button
                    onClick={() => setUnitSystem('us')}
                    className={cn(
                      'px-3 py-1 text-sm font-medium rounded-md transition-colors',
                      unitSystem === 'us'
                        ? 'bg-yellow-400 text-gray-900'
                        : 'text-gray-600 hover:text-gray-900',
                    )}
                  >
                    US
                  </button>
                  <button
                    onClick={() => setUnitSystem('metric')}
                    className={cn(
                      'px-3 py-1 text-sm font-medium rounded-md transition-colors',
                      unitSystem === 'metric'
                        ? 'bg-yellow-400 text-gray-900'
                        : 'text-gray-600 hover:text-gray-900',
                    )}
                  >
                    METRIC
                  </button>
                </div>
              </div>
            </div>

            {/* Dynamic Specification Filters */}
            {specificationsLoading ? (
              <div className="text-gray-500">Loading specifications...</div>
            ) : (
              realSpecifications.map((spec) => renderSpecificationFilter(spec))
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
