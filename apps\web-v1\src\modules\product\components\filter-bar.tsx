import { Trans } from '@lingui/react/macro'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Label,
  Typography,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { useCallback, useEffect, useMemo, useState } from 'react'
import {
  useFilterData,
  useProductTypes,
} from '../../../libs/cms/strapi/use-product-filters'
import type { DynamicFilterSpec } from '../../../libs/types/product'
import { CustomCheckbox } from './custom-checkbox'

//---------------------------------------------------------------------------------
type FilterBarProps = {
  filters: any
  onFilters: (name: string, value: any) => void
}

//---------------------------------------------------------------------------------
export default function FilterBar({ filters, onFilters }: FilterBarProps) {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedSpecificationOptions, setSelectedSpecificationOptions] =
    useState<Record<string, string[]>>({})

  // Fetch dynamic data
  const { categoryOptions, isLoading: categoriesLoading, error: categoriesError } = useProductTypes()
  const { specifications } = useFilterData()

  // Memoize filtered categories for performance
  const filteredCategoryOptions = useMemo(() => {
    return categoryOptions.filter(option => option.count > 0)
  }, [categoryOptions])

  // Sync selectedCategories with filters prop
  useEffect(() => {
    if (filters?.category && Array.isArray(filters.category)) {
      setSelectedCategories(filters.category)
    } else if (filters?.category && typeof filters.category === 'string') {
      setSelectedCategories([filters.category])
    } else {
      setSelectedCategories([])
    }
  }, [filters?.category])

  // Separate effect to handle filter updates to avoid render cycle issues
  useEffect(() => {
    // Only call onFilters if selectedCategories has actually changed
    // and it's not the initial sync from filters prop
    if (selectedCategories.length > 0 || (filters?.category && filters.category.length > 0)) {
      onFilters('category', selectedCategories)
    }
  }, [selectedCategories, onFilters]) // Remove filters?.category dependency to avoid infinite loop

  const handleFilterCategory = useCallback(
    (slug: string, checked: boolean) => {
      setSelectedCategories((prev) => {
        const newSelection = checked
          ? [...prev, slug]
          : prev.filter((item) => item !== slug)
        // Don't call onFilters here - let useEffect handle it
        return newSelection
      })
    },
    [], // Remove onFilters dependency
  )

  const handleClearAllCategories = useCallback(() => {
    setSelectedCategories([])
    // Don't call onFilters here - let useEffect handle it
  }, [])

  const handleSpecificationOptionFilter = useCallback(
    (specSlug: string, optionSlug: string, checked: boolean) => {
      setSelectedSpecificationOptions((prev) => {
        const currentOptions = prev[specSlug] || []
        const newSelection = checked
          ? [...currentOptions, optionSlug]
          : currentOptions.filter((item) => item !== optionSlug)

        const updatedState = {
          ...prev,
          [specSlug]: newSelection,
        }

        onFilters(specSlug, newSelection)
        return updatedState
      })
    },
    [onFilters],
  )

  // Render category filter with improved UI and functionality
  const renderFilterCategory = (
    <div className="flex flex-col gap-4 mt-2">
      <Typography
        variant="h6"
        className="text-gray-800 uppercase font-semibold"
      >
        <Trans>Category</Trans>
      </Typography>
      <div className="flex flex-col gap-3">
        {categoriesLoading ? (
          <div className="flex items-center gap-2 text-gray-500">
            <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
            <span>Loading categories...</span>
          </div>
        ) : categoriesError ? (
          <div className="text-red-500 text-sm">
            <Trans>Error loading categories. Please try again.</Trans>
          </div>
        ) : filteredCategoryOptions.length === 0 ? (
          <div className="text-gray-500 text-sm">No categories available</div>
        ) : (
          <>
            {/* Clear all button */}
            {selectedCategories.length > 0 && (
              <button
                onClick={handleClearAllCategories}
                className="text-sm text-blue-600 hover:text-blue-800 self-start underline transition-colors"
                aria-label="Clear all selected categories"
              >
                <Trans>Clear all</Trans>
              </button>
            )}

            {/* Category options */}
            <div
              className="space-y-2 max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
              role="group"
              aria-label="Product categories"
            >
              {filteredCategoryOptions.map((category) => (
                <div
                  key={category.id}
                  className="flex items-center justify-between group hover:bg-gray-50 p-2 rounded-md transition-colors cursor-pointer"
                  onClick={() => handleFilterCategory(
                    category.value as string,
                    !selectedCategories.includes(category.value as string)
                  )}
                >
                  <div className="flex items-center space-x-3 flex-1">
                    <CustomCheckbox
                      id={category.value as string}
                      checked={selectedCategories.includes(category.value as string)}
                      onCheckedChange={(checked) =>
                        handleFilterCategory(
                          category.value as string,
                          checked as boolean,
                        )
                      }
                      className="cursor-pointer"
                    />
                    <Label
                      className={cn(
                        'cursor-pointer flex-1 text-sm transition-colors select-none',
                        selectedCategories.includes(category.value as string)
                          ? 'font-medium text-gray-900'
                          : 'font-normal text-gray-700 group-hover:text-gray-900',
                      )}
                      htmlFor={category.value as string}
                    >
                      {category.label}
                    </Label>
                  </div>

                  {/* Product count badge */}
                  {category.count !== undefined && category.count > 0 && (
                    <span className={cn(
                      'text-xs px-2 py-1 rounded-full transition-colors ml-2 flex-shrink-0',
                      selectedCategories.includes(category.value as string)
                        ? 'bg-blue-100 text-blue-700 font-medium'
                        : 'bg-gray-100 text-gray-600'
                    )}>
                      {category.count}
                    </span>
                  )}
                </div>
              ))}
            </div>

            {/* Selected count indicator */}
            {selectedCategories.length > 0 && (
              <div className="text-xs text-gray-600 pt-2 border-t">
                <Trans>Selected: {selectedCategories.length} categories</Trans>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )

  // Render dynamic specification filter
  const renderSpecificationFilter = (spec: DynamicFilterSpec) => {
    if (spec.isLoading) {
      return (
        <Accordion
          key={spec.id}
          type="single"
          collapsible
          defaultValue="item-1"
        >
          <AccordionItem value="item-1">
            <AccordionTrigger className="py-0 hover:no-underline cursor-pointer">
              <Typography variant="body1" className="text-gray-700 font-medium">
                <Trans>{spec.name}</Trans>
              </Typography>
            </AccordionTrigger>
            <AccordionContent className="!py-0">
              <div className="text-gray-500">Loading...</div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )
    }

    if (spec.type === 'options') {
      const selectedOptions = selectedSpecificationOptions[spec.slug] || []
      return (
        <Accordion
          key={spec.id}
          type="single"
          collapsible
          defaultValue="item-1"
        >
          <AccordionItem value="item-1">
            <AccordionTrigger className="py-0 hover:no-underline cursor-pointer">
              <Typography variant="body1" className="text-gray-700 font-medium">
                <Trans>{spec.name}</Trans>
              </Typography>
            </AccordionTrigger>
            <AccordionContent className="!py-0">
              <div className="flex flex-col gap-4 mt-4">
                {spec.options.map((option) => (
                  <div
                    key={option.id}
                    className="flex items-center space-x-2 cursor-pointer"
                  >
                    <CustomCheckbox
                      id={`${spec.slug}-${option.value}`}
                      checked={selectedOptions.includes(
                        option.value.toString(),
                      )}
                      onCheckedChange={(checked) =>
                        handleSpecificationOptionFilter(
                          spec.slug,
                          option.value.toString(),
                          checked as boolean,
                        )
                      }
                      className="cursor-pointer"
                    />
                    <Typography variant="body2">
                      <Label
                        className={cn(
                          'cursor-pointer',
                          selectedOptions.includes(option.value.toString())
                            ? 'font-medium'
                            : 'font-normal',
                        )}
                        htmlFor={`${spec.slug}-${option.value}`}
                      >
                        {option.label}
                      </Label>
                    </Typography>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )
    }

    return null
  }

  return (
    <div className="sticky top-4 h-[calc(100vh-2rem)]">
      <div
        className="flex flex-col h-full border border-gray-200/20 rounded-lg bg-white shadow-sm overflow-hidden"
        style={{
          overscrollBehavior: 'contain',
        }}
      >
        <div className="p-6 pb-4 flex-shrink-0">
          <Typography
            variant="h5"
            className="text-gray-800 uppercase font-bold"
          >
            <Trans>FILTER PRODUCTS</Trans>
          </Typography>
        </div>
        <div
          className="flex-1 overflow-y-auto pr-0 pl-6 pb-6 custom-scrollbar"
          style={{
            overscrollBehavior: 'contain',
          }}
          onWheel={(e) => {
            e.stopPropagation()
          }}
        >
          <div className="flex flex-col gap-4 2xl:gap-6 pr-3">
            {renderFilterCategory}

            <Typography
              variant="h6"
              className="text-gray-800 uppercase font-semibold"
            >
              <Trans>Specifications</Trans>
            </Typography>

            {/* Dynamic Specification Filters */}
            {specifications.map((spec) => renderSpecificationFilter(spec))}
          </div>
        </div>
      </div>
    </div>
  )
}
