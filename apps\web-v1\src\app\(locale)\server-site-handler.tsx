import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute, cmsContentType } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import ClientSiteHandler from './client-site-handler'

export default async function ServerSiteHandler() {
  const locale = await getLocale()

  const globalData = await fetchContentType({
    contentType: apiRoute.global,
    params: { locale },
    options: {
      cache: 'force-cache',
      next: {
        revalidate: 60 * 60 * 24,
        tags: ['cms', 'global'],
      },
    },
  })

  const navigationData = await fetchContentType({
    contentType: `${apiRoute.navigationRender}/${cmsContentType.mainNavigation}`,
    params: { locale, type: 'TREE' },
    options: {
      cache: 'force-cache',
      next: {
        revalidate: 60 * 60 * 24,
        tags: ['cms', 'navigation'],
      },
    },
  })

  const topbarData = await fetchContentType({
    contentType: `${apiRoute.navigationRender}/${cmsContentType.topbarNavigation}`,
    params: { locale, type: 'TREE' },
    options: {
      cache: 'force-cache',
      next: {
        revalidate: 60 * 60 * 24,
        tags: ['cms', 'topbar'],
      },
    },
  })

  return (
    <ClientSiteHandler
      globalData={globalData}
      navigationData={navigationData}
      topbarData={topbarData}
      locale={locale}
    />
  )
}
