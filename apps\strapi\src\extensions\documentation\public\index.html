<!-- HTML for static distribution bundle build --><!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <title>Swagger UI</title>
    <link rel="stylesheet" type="text/css" href="/plugins/documentation/swagger-ui.css">
    <link rel="icon" type="image/png" href="/plugins/documentation/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="/plugins/documentation/favicon-16x16.png" sizes="16x16">
    <style>
      html {
        box-sizing: border-box;
        overflow: -moz-scrollbars-vertical;
        overflow-y: scroll;
      }

      *,
      *:before,
      *:after {
        box-sizing: inherit;
      }

      body {
        margin: 0;
        background: #fafafa;
      }
    </style>
  </head>

  <body>
    <div id="swagger-ui"></div>
    <script class="custom-swagger-ui">
      window.onload = function() {
        const ui = SwaggerUIBundle({
          url: "https://petstore.swagger.io/v2/swagger.json",
          spec: {"openapi":"3.0.0","info":{"version":"1.0.0","title":"DOCUMENTATION","description":"","termsOfService":"YOUR_TERMS_OF_SERVICE_URL","contact":{"name":"TEAM","email":"<EMAIL>","url":"mywebsite.io"},"license":{"name":"Apache 2.0","url":"https://www.apache.org/licenses/LICENSE-2.0.html"},"x-generation-date":"2025-06-13T06:01:39.731Z"},"x-strapi-config":{"plugins":["upload","users-permissions"]},"servers":[{"url":"http://localhost:1337/api","description":"Development server"}],"externalDocs":{"description":"Find out more","url":"https://docs.strapi.io/developer-docs/latest/getting-started/introduction.html"},"security":[{"bearerAuth":[]}],"paths":{"/articles":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ArticleListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Article"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/articles"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ArticleResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Article"],"parameters":[],"operationId":"post/articles","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ArticleRequest"}}}}}},"/articles/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ArticleResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Article"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/articles/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ArticleResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Article"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/articles/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ArticleRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Article"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/articles/{id}"}},"/article-categories":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ArticleCategoryListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Article-category"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/article-categories"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ArticleCategoryResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Article-category"],"parameters":[],"operationId":"post/article-categories","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ArticleCategoryRequest"}}}}}},"/article-categories/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ArticleCategoryResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Article-category"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/article-categories/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ArticleCategoryResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Article-category"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/article-categories/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ArticleCategoryRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Article-category"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/article-categories/{id}"}},"/blog-page":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BlogPageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Blog-page"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/blog-page"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BlogPageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Blog-page"],"parameters":[],"operationId":"put/blog-page","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/BlogPageRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Blog-page"],"parameters":[],"operationId":"delete/blog-page"}},"/branches":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BranchListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Branch"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/branches"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BranchResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Branch"],"parameters":[],"operationId":"post/branches","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/BranchRequest"}}}}}},"/branches/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BranchResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Branch"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/branches/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BranchResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Branch"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/branches/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/BranchRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Branch"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/branches/{id}"}},"/brands":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BrandListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Brand"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/brands"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BrandResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Brand"],"parameters":[],"operationId":"post/brands","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/BrandRequest"}}}}}},"/brands/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BrandResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Brand"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/brands/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/BrandResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Brand"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/brands/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/BrandRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Brand"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/brands/{id}"}},"/categories":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CategoryListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Category"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/categories"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CategoryResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Category"],"parameters":[],"operationId":"post/categories","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/CategoryRequest"}}}}}},"/categories/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CategoryResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Category"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/categories/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/CategoryResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Category"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/categories/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/CategoryRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Category"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/categories/{id}"}},"/contact-messages":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ContactMessageListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Contact-message"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/contact-messages"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ContactMessageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Contact-message"],"parameters":[],"operationId":"post/contact-messages","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ContactMessageRequest"}}}}}},"/contact-messages/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ContactMessageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Contact-message"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/contact-messages/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ContactMessageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Contact-message"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/contact-messages/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ContactMessageRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Contact-message"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/contact-messages/{id}"}},"/faqs":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/FaqListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Faq"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/faqs"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/FaqResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Faq"],"parameters":[],"operationId":"post/faqs","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/FaqRequest"}}}}}},"/faqs/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/FaqResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Faq"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/faqs/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/FaqResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Faq"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/faqs/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/FaqRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Faq"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/faqs/{id}"}},"/global":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/GlobalResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Global"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/global"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/GlobalResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Global"],"parameters":[],"operationId":"put/global","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/GlobalRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Global"],"parameters":[],"operationId":"delete/global"}},"/home":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HomeResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Home"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/home"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/HomeResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Home"],"parameters":[],"operationId":"put/home","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/HomeRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Home"],"parameters":[],"operationId":"delete/home"}},"/logos":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/LogoListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Logo"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/logos"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/LogoResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Logo"],"parameters":[],"operationId":"post/logos","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/LogoRequest"}}}}}},"/logos/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/LogoResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Logo"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/logos/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/LogoResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Logo"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/logos/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/LogoRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Logo"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/logos/{id}"}},"/pages":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PageListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Page"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/pages"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Page"],"parameters":[],"operationId":"post/pages","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/PageRequest"}}}}}},"/pages/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Page"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/pages/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/PageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Page"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/pages/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/PageRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Page"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/pages/{id}"}},"/products":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ProductListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Product"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/products"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ProductResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Product"],"parameters":[],"operationId":"post/products","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ProductRequest"}}}}}},"/products/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ProductResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Product"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/products/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ProductResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Product"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/products/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ProductRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Product"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/products/{id}"}},"/product-page":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ProductPageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Product-page"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/product-page"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/ProductPageResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Product-page"],"parameters":[],"operationId":"put/product-page","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/ProductPageRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Product-page"],"parameters":[],"operationId":"delete/product-page"}},"/redirections":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/RedirectionListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Redirection"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/redirections"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/RedirectionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Redirection"],"parameters":[],"operationId":"post/redirections","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/RedirectionRequest"}}}}}},"/redirections/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/RedirectionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Redirection"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/redirections/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/RedirectionResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Redirection"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/redirections/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/RedirectionRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Redirection"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/redirections/{id}"}},"/team-members":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/TeamMemberListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Team-member"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/team-members"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/TeamMemberResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Team-member"],"parameters":[],"operationId":"post/team-members","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/TeamMemberRequest"}}}}}},"/team-members/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/TeamMemberResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Team-member"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/team-members/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/TeamMemberResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Team-member"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/team-members/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/TeamMemberRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Team-member"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/team-members/{id}"}},"/testimonials":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/TestimonialListResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Testimonial"],"parameters":[{"name":"sort","in":"query","description":"Sort by attributes ascending (asc) or descending (desc)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"pagination[withCount]","in":"query","description":"Return page/pageSize (default: true)","deprecated":false,"required":false,"schema":{"type":"boolean"}},{"name":"pagination[page]","in":"query","description":"Page number (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[pageSize]","in":"query","description":"Page size (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[start]","in":"query","description":"Offset value (default: 0)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"pagination[limit]","in":"query","description":"Number of entities to return (default: 25)","deprecated":false,"required":false,"schema":{"type":"integer"}},{"name":"fields","in":"query","description":"Fields to return (ex: title,author)","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"populate","in":"query","description":"Relations to return","deprecated":false,"required":false,"schema":{"type":"string"}},{"name":"filters","in":"query","description":"Filters to apply","deprecated":false,"required":false,"schema":{"type":"object","additionalProperties":true},"style":"deepObject"},{"name":"locale","in":"query","description":"Locale to apply","deprecated":false,"required":false,"schema":{"type":"string"}}],"operationId":"get/testimonials"},"post":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/TestimonialResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Testimonial"],"parameters":[],"operationId":"post/testimonials","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/TestimonialRequest"}}}}}},"/testimonials/{id}":{"get":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/TestimonialResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Testimonial"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"get/testimonials/{id}"},"put":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"$ref":"#/components/schemas/TestimonialResponse"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Testimonial"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"put/testimonials/{id}","requestBody":{"required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/TestimonialRequest"}}}}},"delete":{"responses":{"200":{"description":"OK","content":{"application/json":{"schema":{"type":"integer","format":"int64"}}}},"400":{"description":"Bad Request","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"401":{"description":"Unauthorized","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"403":{"description":"Forbidden","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"404":{"description":"Not Found","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}},"500":{"description":"Internal Server Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}},"tags":["Testimonial"],"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"number"}}],"operationId":"delete/testimonials/{id}"}},"/upload":{"post":{"description":"Upload files","responses":{"200":{"description":"response","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/UploadFile"}}}}}},"summary":"","tags":["Upload - File"],"requestBody":{"description":"Upload files","required":true,"content":{"multipart/form-data":{"schema":{"required":["files"],"type":"object","properties":{"path":{"type":"string","description":"The folder where the file(s) will be uploaded to (only supported on strapi-provider-upload-aws-s3)."},"refId":{"type":"string","description":"The ID of the entry which the file(s) will be linked to"},"ref":{"type":"string","description":"The unique ID (uid) of the model which the file(s) will be linked to (api::restaurant.restaurant)."},"field":{"type":"string","description":"The field of the entry which the file(s) will be precisely linked to."},"files":{"type":"array","items":{"type":"string","format":"binary"}}}}}}}}},"/upload?id={id}":{"post":{"parameters":[{"name":"id","in":"query","description":"File id","required":true,"schema":{"type":"string"}}],"description":"Upload file information","responses":{"200":{"description":"response","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/UploadFile"}}}}}},"summary":"","tags":["Upload - File"],"requestBody":{"description":"Upload files","required":true,"content":{"multipart/form-data":{"schema":{"type":"object","properties":{"fileInfo":{"type":"object","properties":{"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"}}},"files":{"type":"string","format":"binary"}}}}}}}},"/upload/files":{"get":{"tags":["Upload - File"],"responses":{"200":{"description":"Get a list of files","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/UploadFile"}}}}}}}},"/upload/files/{id}":{"get":{"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"string"}}],"tags":["Upload - File"],"responses":{"200":{"description":"Get a specific file","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UploadFile"}}}}}},"delete":{"parameters":[{"name":"id","in":"path","description":"","deprecated":false,"required":true,"schema":{"type":"string"}}],"tags":["Upload - File"],"responses":{"200":{"description":"Delete a file","content":{"application/json":{"schema":{"$ref":"#/components/schemas/UploadFile"}}}}}}},"/connect/{provider}":{"get":{"parameters":[{"name":"provider","in":"path","required":true,"description":"Provider name","schema":{"type":"string","pattern":".*"}}],"tags":["Users-Permissions - Auth"],"summary":"Login with a provider","description":"Redirects to provider login before being redirect to /auth/{provider}/callback","responses":{"301":{"description":"Redirect response"},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/local":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Local login","description":"Returns a jwt token and user info","requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"identifier":{"type":"string"},"password":{"type":"string"}}},"example":{"identifier":"foobar","password":"Test1234"}}},"required":true},"responses":{"200":{"description":"Connection","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/local/register":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Register a user","description":"Returns a jwt token and user info","requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"username":{"type":"string"},"email":{"type":"string"},"password":{"type":"string"}}},"example":{"username":"foobar","email":"<EMAIL>","password":"Test1234"}}},"required":true},"responses":{"200":{"description":"Successful registration","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/{provider}/callback":{"get":{"tags":["Users-Permissions - Auth"],"summary":"Default Callback from provider auth","parameters":[{"name":"provider","in":"path","required":true,"description":"Provider name","schema":{"type":"string"}}],"responses":{"200":{"description":"Returns a jwt token and user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/forgot-password":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Send rest password email","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"email":{"type":"string"}}},"example":{"email":"<EMAIL>"}}}},"responses":{"200":{"description":"Returns ok","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/reset-password":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Rest user password","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"password":{"type":"string"},"passwordConfirmation":{"type":"string"},"code":{"type":"string"}}},"example":{"password":"Test1234","passwordConfirmation":"Test1234","code":"zertyoaizndoianzodianzdonaizdoinaozdnia"}}}},"responses":{"200":{"description":"Returns a jwt token and user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/change-password":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Update user's own password","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["password","currentPassword","passwordConfirmation"],"properties":{"password":{"type":"string"},"currentPassword":{"type":"string"},"passwordConfirmation":{"type":"string"}}}}}},"responses":{"200":{"description":"Returns a jwt token and user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-UserRegistration"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/email-confirmation":{"get":{"tags":["Users-Permissions - Auth"],"summary":"Confirm user email","parameters":[{"in":"query","name":"confirmation","schema":{"type":"string"},"description":"confirmation token received by email"}],"responses":{"301":{"description":"Redirects to the configure email confirmation redirect url"},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/auth/send-email-confirmation":{"post":{"tags":["Users-Permissions - Auth"],"summary":"Send confirmation email","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"email":{"type":"string"}}}}}},"responses":{"200":{"description":"Returns email and boolean to confirm email was sent","content":{"application/json":{"schema":{"type":"object","properties":{"email":{"type":"string"},"sent":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/permissions":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get default generated permissions","responses":{"200":{"description":"Returns the permissions tree","content":{"application/json":{"schema":{"type":"object","properties":{"permissions":{"$ref":"#/components/schemas/Users-Permissions-PermissionsTree"}}},"example":{"permissions":{"api::content-type.content-type":{"controllers":{"controllerA":{"find":{"enabled":false,"policy":""},"findOne":{"enabled":false,"policy":""},"create":{"enabled":false,"policy":""}},"controllerB":{"find":{"enabled":false,"policy":""},"findOne":{"enabled":false,"policy":""},"create":{"enabled":false,"policy":""}}}}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/roles":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"List roles","responses":{"200":{"description":"Returns list of roles","content":{"application/json":{"schema":{"type":"object","properties":{"roles":{"type":"array","items":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-Role"},{"type":"object","properties":{"nb_users":{"type":"number"}}}]}}}},"example":{"roles":[{"id":1,"name":"Public","description":"Default role given to unauthenticated user.","type":"public","createdAt":"2022-05-19T17:35:35.097Z","updatedAt":"2022-05-31T16:05:36.603Z","nb_users":0}]}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"post":{"tags":["Users-Permissions - Users & Roles"],"summary":"Create a role","requestBody":{"$ref":"#/components/requestBodies/Users-Permissions-RoleRequest"},"responses":{"200":{"description":"Returns ok if the role was create","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/roles/{id}":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get a role","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"role Id"}],"responses":{"200":{"description":"Returns the role","content":{"application/json":{"schema":{"type":"object","properties":{"role":{"$ref":"#/components/schemas/Users-Permissions-Role"}}},"example":{"role":{"id":1,"name":"Public","description":"Default role given to unauthenticated user.","type":"public","createdAt":"2022-05-19T17:35:35.097Z","updatedAt":"2022-05-31T16:05:36.603Z","permissions":{"api::content-type.content-type":{"controllers":{"controllerA":{"find":{"enabled":true}}}}}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users-permissions/roles/{role}":{"put":{"tags":["Users-Permissions - Users & Roles"],"summary":"Update a role","parameters":[{"in":"path","name":"role","required":true,"schema":{"type":"string"},"description":"role Id"}],"requestBody":{"$ref":"#/components/requestBodies/Users-Permissions-RoleRequest"},"responses":{"200":{"description":"Returns ok if the role was udpated","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"delete":{"tags":["Users-Permissions - Users & Roles"],"summary":"Delete a role","parameters":[{"in":"path","name":"role","required":true,"schema":{"type":"string"},"description":"role Id"}],"responses":{"200":{"description":"Returns ok if the role was delete","content":{"application/json":{"schema":{"type":"object","properties":{"ok":{"type":"string","enum":[true]}}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get list of users","responses":{"200":{"description":"Returns an array of users","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/Users-Permissions-User"}},"example":[{"id":9,"username":"<EMAIL>","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-06-01T18:32:35.211Z","updatedAt":"2022-06-01T18:32:35.217Z"}]}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"post":{"tags":["Users-Permissions - Users & Roles"],"summary":"Create a user","requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["username","email","password"],"properties":{"email":{"type":"string"},"username":{"type":"string"},"password":{"type":"string"}}},"example":{"username":"foo","email":"<EMAIL>","password":"foo-password"}}}},"responses":{"201":{"description":"Returns created user info","content":{"application/json":{"schema":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-User"},{"type":"object","properties":{"role":{"$ref":"#/components/schemas/Users-Permissions-Role"}}}]},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z","role":{"id":1,"name":"X","description":"Default role given to authenticated user.","type":"authenticated","createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-06-04T07:11:59.551Z"}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users/{id}":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get a user","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"user Id"}],"responses":{"200":{"description":"Returns a user","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-User"},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"put":{"tags":["Users-Permissions - Users & Roles"],"summary":"Update a user","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"user Id"}],"requestBody":{"required":true,"content":{"application/json":{"schema":{"type":"object","required":["username","email","password"],"properties":{"email":{"type":"string"},"username":{"type":"string"},"password":{"type":"string"}}},"example":{"username":"foo","email":"<EMAIL>","password":"foo-password"}}}},"responses":{"200":{"description":"Returns updated user info","content":{"application/json":{"schema":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-User"},{"type":"object","properties":{"role":{"$ref":"#/components/schemas/Users-Permissions-Role"}}}]},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z","role":{"id":1,"name":"X","description":"Default role given to authenticated user.","type":"authenticated","createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-06-04T07:11:59.551Z"}}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}},"delete":{"tags":["Users-Permissions - Users & Roles"],"summary":"Delete a user","parameters":[{"in":"path","name":"id","required":true,"schema":{"type":"string"},"description":"user Id"}],"responses":{"200":{"description":"Returns deleted user info","content":{"application/json":{"schema":{"allOf":[{"$ref":"#/components/schemas/Users-Permissions-User"}]},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users/me":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get authenticated user info","responses":{"200":{"description":"Returns user info","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Users-Permissions-User"},"example":{"id":1,"username":"foo","email":"<EMAIL>","provider":"local","confirmed":false,"blocked":false,"createdAt":"2022-05-19T17:35:35.096Z","updatedAt":"2022-05-19T17:35:35.096Z"}}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}},"/users/count":{"get":{"tags":["Users-Permissions - Users & Roles"],"summary":"Get user count","responses":{"200":{"description":"Returns a number","content":{"application/json":{"schema":{"type":"number"},"example":1}}},"default":{"description":"Error","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Error"}}}}}}}},"components":{"securitySchemes":{"bearerAuth":{"type":"http","scheme":"bearer","bearerFormat":"JWT"}},"schemas":{"Error":{"type":"object","required":["error"],"properties":{"data":{"nullable":true,"oneOf":[{"type":"object"},{"type":"array","items":{"type":"object"}}]},"error":{"type":"object","properties":{"status":{"type":"integer"},"name":{"type":"string"},"message":{"type":"string"},"details":{"type":"object"}}}}},"ArticleRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"seo":{"$ref":"#/components/schemas/SharedSeoComponent"},"title":{"type":"string"},"description":{"type":"string"},"slug":{"type":"string"},"content":{},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneRelatedArticlesComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.related-articles":"#/components/schemas/DynamicZoneRelatedArticlesComponent"}}},"image":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"category":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"ArticleListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Article"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Article":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"seo":{"$ref":"#/components/schemas/SharedSeoComponent"},"title":{"type":"string"},"description":{"type":"string"},"slug":{"type":"string"},"content":{},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneRelatedArticlesComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.related-articles":"#/components/schemas/DynamicZoneRelatedArticlesComponent"}}},"image":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"category":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"ArticleResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Article"},"meta":{"type":"object"}}},"SharedSeoComponent":{"type":"object","properties":{"id":{"type":"number"},"metaTitle":{"type":"string"},"metaDescription":{"type":"string"},"metaImage":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"keywords":{"type":"string"},"metaRobots":{"type":"string"},"structuredData":{},"metaViewport":{"type":"string"},"canonicalURL":{"type":"string"}}},"DynamicZoneRelatedArticlesComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.related-articles"]},"heading":{"type":"string"},"sub_heading":{"type":"string"},"articles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"ArticleCategoryRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["title"],"type":"object","properties":{"adminLabel":{"type":"string"},"title":{"type":"string"},"slug":{"type":"string"},"children":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"parent":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"articles":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"type":{"type":"string","enum":["default","promotion"]},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"ArticleCategoryListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/ArticleCategory"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"ArticleCategory":{"type":"object","required":["title"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"adminLabel":{"type":"string"},"title":{"type":"string"},"slug":{"type":"string"},"children":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"adminLabel":{"type":"string"},"title":{"type":"string"},"slug":{"type":"string"},"children":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"parent":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"articles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"seo":{"$ref":"#/components/schemas/SharedSeoComponent"},"title":{"type":"string"},"description":{"type":"string"},"slug":{"type":"string"},"content":{},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneRelatedArticlesComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.related-articles":"#/components/schemas/DynamicZoneRelatedArticlesComponent"}}},"image":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"category":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"type":{"type":"string","enum":["default","promotion"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"parent":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"articles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"type":{"type":"string","enum":["default","promotion"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"ArticleCategoryResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/ArticleCategory"},"meta":{"type":"object"}}},"BlogPageRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"heading":{"type":"string"},"sub_heading":{"type":"string"},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"},{"$ref":"#/components/schemas/DynamicZoneRelatedArticlesComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturesComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent","dynamic-zone.related-articles":"#/components/schemas/DynamicZoneRelatedArticlesComponent","dynamic-zone.features":"#/components/schemas/DynamicZoneFeaturesComponent"}}},"seo":{"$ref":"#/components/schemas/SharedSeoComponent"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"BlogPageListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/BlogPage"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"BlogPage":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"heading":{"type":"string"},"sub_heading":{"type":"string"},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"},{"$ref":"#/components/schemas/DynamicZoneRelatedArticlesComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturesComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent","dynamic-zone.related-articles":"#/components/schemas/DynamicZoneRelatedArticlesComponent","dynamic-zone.features":"#/components/schemas/DynamicZoneFeaturesComponent"}}},"seo":{"$ref":"#/components/schemas/SharedSeoComponent"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"heading":{"type":"string"},"sub_heading":{"type":"string"},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"},{"$ref":"#/components/schemas/DynamicZoneRelatedArticlesComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturesComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent","dynamic-zone.related-articles":"#/components/schemas/DynamicZoneRelatedArticlesComponent","dynamic-zone.features":"#/components/schemas/DynamicZoneFeaturesComponent"}}},"seo":{"$ref":"#/components/schemas/SharedSeoComponent"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"BlogPageResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/BlogPage"},"meta":{"type":"object"}}},"DynamicZoneRelatedProductsComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.related-products"]},"heading":{"type":"string"},"sub_heading":{"type":"string"},"products":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"CardsGlobeCardComponent":{"type":"object","properties":{"id":{"type":"number"},"title":{"type":"string"},"description":{"type":"string"},"span":{"type":"string","enum":["one","two","three"]}}},"ItemsRayItemsComponent":{"type":"object","properties":{"id":{"type":"number"},"item_1":{"type":"string"},"item_2":{"type":"string"},"item_3":{"type":"string"}}},"CardsRayCardComponent":{"type":"object","properties":{"id":{"type":"number"},"title":{"type":"string"},"description":{"type":"string"},"before_ray_items":{"$ref":"#/components/schemas/ItemsRayItemsComponent"},"after_ray_items":{"$ref":"#/components/schemas/ItemsRayItemsComponent"},"span":{"type":"string","enum":["one","two","three"]}}},"ItemsGraphCardTopItemsComponent":{"type":"object","properties":{"id":{"type":"number"},"number":{"type":"string"},"text":{"type":"string"}}},"CardsGraphCardComponent":{"type":"object","properties":{"id":{"type":"number"},"title":{"type":"string"},"description":{"type":"string"},"top_items":{"type":"array","items":{"$ref":"#/components/schemas/ItemsGraphCardTopItemsComponent"}},"highlighted_text":{"type":"string"},"span":{"type":"string","enum":["one","two","three"]}}},"CardsSocialMediaCardComponent":{"type":"object","properties":{"id":{"type":"number"},"Title":{"type":"string"},"Description":{"type":"string"},"span":{"type":"string","enum":["one","two","three"]}}},"DynamicZoneFeaturesComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.features"]},"heading":{"type":"string"},"sub_heading":{"type":"string"},"globe_card":{"$ref":"#/components/schemas/CardsGlobeCardComponent"},"ray_card":{"$ref":"#/components/schemas/CardsRayCardComponent"},"graph_card":{"$ref":"#/components/schemas/CardsGraphCardComponent"},"social_media_card":{"$ref":"#/components/schemas/CardsSocialMediaCardComponent"}}},"BranchRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["name"],"type":"object","properties":{"adminLabel":{"type":"string"},"name":{"type":"string"},"contact_items":{"type":"array","items":{"$ref":"#/components/schemas/ItemsContactItemComponent"}},"map_url":{"type":"string"},"images":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"BranchListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Branch"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Branch":{"type":"object","required":["name"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"adminLabel":{"type":"string"},"name":{"type":"string"},"contact_items":{"type":"array","items":{"$ref":"#/components/schemas/ItemsContactItemComponent"}},"map_url":{"type":"string"},"images":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"pathId":{"type":"integer"},"parent":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"children":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"files":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"path":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"adminLabel":{"type":"string"},"name":{"type":"string"},"contact_items":{"type":"array","items":{"$ref":"#/components/schemas/ItemsContactItemComponent"}},"map_url":{"type":"string"},"images":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"BranchResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Branch"},"meta":{"type":"object"}}},"ItemsContactItemComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"title":{"type":"string"},"description":{"type":"string"}}},"BrandRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["name"],"type":"object","properties":{"adminLabel":{"type":"string"},"name":{"type":"string"},"slug":{"type":"string"},"logo":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"excerpt":{"type":"string"},"description":{"type":"string"},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneVideoSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"},{"$ref":"#/components/schemas/DynamicZonePageHeroSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneMediaTextSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneLatestNewsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneIconBoxSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneFormSubscribeComponent"},{"$ref":"#/components/schemas/DynamicZoneAccordionSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent"},{"$ref":"#/components/schemas/DynamicZoneImageBoxSectionComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.video-section":"#/components/schemas/DynamicZoneVideoSectionComponent","dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent","dynamic-zone.page-hero-section":"#/components/schemas/DynamicZonePageHeroSectionComponent","dynamic-zone.media-text-section":"#/components/schemas/DynamicZoneMediaTextSectionComponent","dynamic-zone.latest-news-section":"#/components/schemas/DynamicZoneLatestNewsSectionComponent","dynamic-zone.icon-box-section":"#/components/schemas/DynamicZoneIconBoxSectionComponent","dynamic-zone.hover-overlay-card-collection":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent","dynamic-zone.hover-expand-card-collection":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent","dynamic-zone.form-subscribe":"#/components/schemas/DynamicZoneFormSubscribeComponent","dynamic-zone.accordion-section":"#/components/schemas/DynamicZoneAccordionSectionComponent","dynamic-zone.featured-promotions":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent","dynamic-zone.image-box-section":"#/components/schemas/DynamicZoneImageBoxSectionComponent"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"BrandListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Brand"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Brand":{"type":"object","required":["name"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"adminLabel":{"type":"string"},"name":{"type":"string"},"slug":{"type":"string"},"logo":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"pathId":{"type":"integer"},"parent":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"children":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"files":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"path":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"excerpt":{"type":"string"},"description":{"type":"string"},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneVideoSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"},{"$ref":"#/components/schemas/DynamicZonePageHeroSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneMediaTextSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneLatestNewsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneIconBoxSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneFormSubscribeComponent"},{"$ref":"#/components/schemas/DynamicZoneAccordionSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent"},{"$ref":"#/components/schemas/DynamicZoneImageBoxSectionComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.video-section":"#/components/schemas/DynamicZoneVideoSectionComponent","dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent","dynamic-zone.page-hero-section":"#/components/schemas/DynamicZonePageHeroSectionComponent","dynamic-zone.media-text-section":"#/components/schemas/DynamicZoneMediaTextSectionComponent","dynamic-zone.latest-news-section":"#/components/schemas/DynamicZoneLatestNewsSectionComponent","dynamic-zone.icon-box-section":"#/components/schemas/DynamicZoneIconBoxSectionComponent","dynamic-zone.hover-overlay-card-collection":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent","dynamic-zone.hover-expand-card-collection":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent","dynamic-zone.form-subscribe":"#/components/schemas/DynamicZoneFormSubscribeComponent","dynamic-zone.accordion-section":"#/components/schemas/DynamicZoneAccordionSectionComponent","dynamic-zone.featured-promotions":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent","dynamic-zone.image-box-section":"#/components/schemas/DynamicZoneImageBoxSectionComponent"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"adminLabel":{"type":"string"},"name":{"type":"string"},"slug":{"type":"string"},"logo":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"excerpt":{"type":"string"},"description":{"type":"string"},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneVideoSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"},{"$ref":"#/components/schemas/DynamicZonePageHeroSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneMediaTextSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneLatestNewsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneIconBoxSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneFormSubscribeComponent"},{"$ref":"#/components/schemas/DynamicZoneAccordionSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent"},{"$ref":"#/components/schemas/DynamicZoneImageBoxSectionComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.video-section":"#/components/schemas/DynamicZoneVideoSectionComponent","dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent","dynamic-zone.page-hero-section":"#/components/schemas/DynamicZonePageHeroSectionComponent","dynamic-zone.media-text-section":"#/components/schemas/DynamicZoneMediaTextSectionComponent","dynamic-zone.latest-news-section":"#/components/schemas/DynamicZoneLatestNewsSectionComponent","dynamic-zone.icon-box-section":"#/components/schemas/DynamicZoneIconBoxSectionComponent","dynamic-zone.hover-overlay-card-collection":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent","dynamic-zone.hover-expand-card-collection":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent","dynamic-zone.form-subscribe":"#/components/schemas/DynamicZoneFormSubscribeComponent","dynamic-zone.accordion-section":"#/components/schemas/DynamicZoneAccordionSectionComponent","dynamic-zone.featured-promotions":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent","dynamic-zone.image-box-section":"#/components/schemas/DynamicZoneImageBoxSectionComponent"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"BrandResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Brand"},"meta":{"type":"object"}}},"StylesBackgroundStylesComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"image":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"color":{"type":"string"},"opacity":{"type":"number","format":"float"},"repeat":{"type":"string","enum":["no-repeat","repeat","repeat-x","repeat-y"]},"size":{"type":"string","enum":["cover","contain","auto"]}}},"StylesSectionStylesComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"background":{"$ref":"#/components/schemas/StylesBackgroundStylesComponent"},"content_max_width":{"type":"string","enum":["full_width","xl","lg","md","sm","xs"]}}},"StylesTextStylesComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"color":{"type":"string"},"font_weight":{"type":"string","enum":["font-black","font-extrabold","font-bold","font-semibold","font-medium","font-normal","font-light","font-extralight","font-thin"]},"text_align":{"type":"string","enum":["text-left","text-center","text-right","text-justify"]},"line_height":{"type":"number","format":"float"},"variant":{"type":"string","enum":["h1","h2","h3","h4","h5","h6","body1","body2","caption"]}}},"ItemsTextItemComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"text":{"type":"string"},"styles":{"$ref":"#/components/schemas/StylesTextStylesComponent"}}},"SharedButtonComponent":{"type":"object","properties":{"id":{"type":"number"},"text":{"type":"string"},"URL":{"type":"string"},"target":{"type":"string","enum":["_blank","_self","_parent","_top"]},"variant":{"type":"string","enum":["simple","outline","primary","muted"]}}},"ElementalsHeadingComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"type":{"type":"string","enum":["default","grid"]},"heading":{"$ref":"#/components/schemas/ItemsTextItemComponent"},"sub_heading":{"$ref":"#/components/schemas/ItemsTextItemComponent"},"buttons":{"type":"array","items":{"$ref":"#/components/schemas/SharedButtonComponent"}}}},"ElementalsMediaVideoComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"video":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"embed":{"type":"string"},"image_poster":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"open_type":{"type":"string","enum":["playin","dialog","new_tab"]},"autoplay":{"type":"boolean"},"loop":{"type":"boolean"},"border_radius":{"type":"integer"}}},"DynamicZoneVideoSectionComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.video-section"]},"adminLabel":{"type":"string"},"styles":{"$ref":"#/components/schemas/StylesSectionStylesComponent"},"heading":{"$ref":"#/components/schemas/ElementalsHeadingComponent"},"video":{"$ref":"#/components/schemas/ElementalsMediaVideoComponent"}}},"ItemsBreadcrumbItemComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"text":{"type":"string"},"url":{"type":"string"}}},"ElementalsMediaImageComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"image":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"alt":{"type":"string"},"border_radius":{"type":"integer"}}},"DynamicZonePageHeroSectionComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.page-hero-section"]},"adminLabel":{"type":"string"},"styles":{"$ref":"#/components/schemas/StylesSectionStylesComponent"},"breadcrumb_list":{"type":"array","items":{"$ref":"#/components/schemas/ItemsBreadcrumbItemComponent"}},"heading":{"$ref":"#/components/schemas/ElementalsHeadingComponent"},"image":{"$ref":"#/components/schemas/ElementalsMediaImageComponent"},"video":{"$ref":"#/components/schemas/ElementalsMediaVideoComponent"}}},"DynamicZoneMediaTextSectionComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.media-text-section"]},"adminLabel":{"type":"string"},"styles":{"$ref":"#/components/schemas/StylesSectionStylesComponent"},"layout":{"type":"string","enum":["split","large-media"]},"image_position":{"type":"string","enum":["left","right"]},"container_styles":{"$ref":"#/components/schemas/StylesBackgroundStylesComponent"},"heading":{"$ref":"#/components/schemas/ItemsTextItemComponent"},"sub_heading":{"$ref":"#/components/schemas/ItemsTextItemComponent"},"description":{},"buttons":{"type":"array","items":{"$ref":"#/components/schemas/SharedButtonComponent"}},"image":{"$ref":"#/components/schemas/ElementalsMediaImageComponent"}}},"DynamicZoneLatestNewsSectionComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.latest-news-section"]},"adminLabel":{"type":"string"},"styles":{"$ref":"#/components/schemas/StylesSectionStylesComponent"},"heading":{"$ref":"#/components/schemas/ElementalsHeadingComponent"}}},"ElementalsIconBoxComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"icon":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"icon_position":{"type":"string","enum":["top","center","left"]},"title":{"type":"string"},"sub_title":{"type":"string"},"text_align":{"type":"string","enum":["left","center","right","justify"]}}},"DynamicZoneIconBoxSectionComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.icon-box-section"]},"adminLabel":{"type":"string"},"styles":{"$ref":"#/components/schemas/StylesSectionStylesComponent"},"heading":{"$ref":"#/components/schemas/ElementalsHeadingComponent"},"items":{"type":"array","items":{"$ref":"#/components/schemas/ElementalsIconBoxComponent"}}}},"DynamicZoneHoverOverlayCardCollectionComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.hover-overlay-card-collection"]},"adminLabel":{"type":"string"},"styles":{"$ref":"#/components/schemas/StylesSectionStylesComponent"},"heading":{"$ref":"#/components/schemas/ElementalsHeadingComponent"}}},"CardsHoverExpandCardComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"content":{"$ref":"#/components/schemas/ElementalsHeadingComponent"},"background":{"$ref":"#/components/schemas/StylesBackgroundStylesComponent"}}},"DynamicZoneHoverExpandCardCollectionComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.hover-expand-card-collection"]},"adminLabel":{"type":"string"},"styles":{"$ref":"#/components/schemas/StylesSectionStylesComponent"},"heading":{"$ref":"#/components/schemas/ElementalsHeadingComponent"},"items":{"type":"array","items":{"$ref":"#/components/schemas/CardsHoverExpandCardComponent"}}}},"SharedSocialLinkComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"name":{"type":"string","enum":["facebook","linkedin","youtube","tiktok","email","phone"]},"url":{"type":"string"},"target":{"type":"string","enum":["_blank","_self","_parent"]}}},"DynamicZoneFormSubscribeComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.form-subscribe"]},"adminLabel":{"type":"string"},"styles":{"$ref":"#/components/schemas/StylesSectionStylesComponent"},"heading":{"$ref":"#/components/schemas/ElementalsHeadingComponent"},"socials":{"type":"array","items":{"$ref":"#/components/schemas/SharedSocialLinkComponent"}}}},"ItemsAccordionItemComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"title":{"type":"string"},"description":{"type":"string"},"buttons":{"type":"array","items":{"$ref":"#/components/schemas/SharedButtonComponent"}}}},"DynamicZoneAccordionSectionComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.accordion-section"]},"adminLabel":{"type":"string"},"styles":{"$ref":"#/components/schemas/StylesSectionStylesComponent"},"items":{"type":"array","items":{"$ref":"#/components/schemas/ItemsAccordionItemComponent"}},"image":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"heading":{"$ref":"#/components/schemas/ElementalsHeadingComponent"}}},"DynamicZoneFeaturedPromotionsComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.featured-promotions"]},"adminLabel":{"type":"string"},"styles":{"$ref":"#/components/schemas/StylesSectionStylesComponent"},"heading":{"$ref":"#/components/schemas/ElementalsHeadingComponent"}}},"ElementalsImageBoxComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"image":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"title":{"type":"string"},"sub_title":{"type":"string"},"text_align":{"type":"string","enum":["left","center","right","justify"]}}},"DynamicZoneImageBoxSectionComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.image-box-section"]},"adminLabel":{"type":"string"},"type":{"type":"string","enum":["timeline","grid","slide"]},"styles":{"$ref":"#/components/schemas/StylesSectionStylesComponent"},"heading":{"$ref":"#/components/schemas/ElementalsHeadingComponent"},"items":{"type":"array","items":{"$ref":"#/components/schemas/ElementalsImageBoxComponent"}}}},"CategoryRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"name":{"type":"string"},"product":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"CategoryListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Category"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Category":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"product":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"price":{"type":"integer"},"description":{"type":"string"},"slug":{"type":"string"},"images":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"pathId":{"type":"integer"},"parent":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"children":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"files":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"path":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"categories":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"product":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent"}}},"featured":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"CategoryResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Category"},"meta":{"type":"object"}}},"ContactMessageRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"name":{"type":"string"},"company_name":{"type":"string"},"phone_number":{"type":"string"},"email":{"type":"string","format":"email"},"message":{"type":"string"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"ContactMessageListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/ContactMessage"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"ContactMessage":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"company_name":{"type":"string"},"phone_number":{"type":"string"},"email":{"type":"string","format":"email"},"message":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"company_name":{"type":"string"},"phone_number":{"type":"string"},"email":{"type":"string","format":"email"},"message":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"ContactMessageResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/ContactMessage"},"meta":{"type":"object"}}},"FaqRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"question":{"type":"string"},"answer":{"type":"string"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"FaqListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Faq"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Faq":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"question":{"type":"string"},"answer":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"question":{"type":"string"},"answer":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"FaqResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Faq"},"meta":{"type":"object"}}},"GlobalRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"seo":{"$ref":"#/components/schemas/SharedSeoComponent"},"navbar":{"$ref":"#/components/schemas/GlobalNavbarComponent"},"footer":{"$ref":"#/components/schemas/GlobalFooterComponent"},"header":{"$ref":"#/components/schemas/GlobalHeaderComponent"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"GlobalListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Global"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Global":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"seo":{"$ref":"#/components/schemas/SharedSeoComponent"},"navbar":{"$ref":"#/components/schemas/GlobalNavbarComponent"},"footer":{"$ref":"#/components/schemas/GlobalFooterComponent"},"header":{"$ref":"#/components/schemas/GlobalHeaderComponent"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"seo":{"$ref":"#/components/schemas/SharedSeoComponent"},"navbar":{"$ref":"#/components/schemas/GlobalNavbarComponent"},"footer":{"$ref":"#/components/schemas/GlobalFooterComponent"},"header":{"$ref":"#/components/schemas/GlobalHeaderComponent"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"GlobalResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Global"},"meta":{"type":"object"}}},"SharedLinkComponent":{"type":"object","properties":{"id":{"type":"number"},"text":{"type":"string"},"URL":{"type":"string"},"target":{"type":"string","enum":["_blank","_self","_parent","_top"]}}},"GlobalNavbarComponent":{"type":"object","properties":{"id":{"type":"number"},"logo":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"left_navbar_items":{"type":"array","items":{"$ref":"#/components/schemas/SharedLinkComponent"}},"right_navbar_items":{"type":"array","items":{"$ref":"#/components/schemas/SharedLinkComponent"}}}},"ItemsFooterNavItemComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"title":{"type":"string"},"links":{"type":"array","items":{"$ref":"#/components/schemas/SharedLinkComponent"}}}},"ElementalsFooterContactComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"title":{"type":"string"},"items":{"type":"array","items":{"$ref":"#/components/schemas/ItemsContactItemComponent"}}}},"GlobalFooterComponent":{"type":"object","properties":{"id":{"type":"number"},"logo":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"description":{"type":"string"},"copyright":{"type":"string"},"built_with":{"type":"string"},"policy_links":{"type":"array","items":{"$ref":"#/components/schemas/SharedLinkComponent"}},"social_media_links":{"type":"array","items":{"$ref":"#/components/schemas/SharedLinkComponent"}},"footer_nav":{"type":"array","items":{"$ref":"#/components/schemas/ItemsFooterNavItemComponent"}},"attached_images":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"footer_contact":{"$ref":"#/components/schemas/ElementalsFooterContactComponent"}}},"GlobalHeaderComponent":{"type":"object","properties":{"id":{"type":"number"},"header_buttons":{"type":"array","items":{"$ref":"#/components/schemas/SharedButtonComponent"}},"logo":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}},"HomeRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["heading"],"type":"object","properties":{"heading":{"type":"string"},"subHeading":{"type":"string"},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneVideoSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneTestimonialsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"},{"$ref":"#/components/schemas/DynamicZoneRelatedArticlesComponent"},{"$ref":"#/components/schemas/DynamicZonePageHeroSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneMediaTextSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneLocationsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneLatestNewsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneIconBoxSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHeroSlideSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneFormSubscribeComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturesComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent"},{"$ref":"#/components/schemas/DynamicZoneExecutiveTeamSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneAccordionSectionComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.video-section":"#/components/schemas/DynamicZoneVideoSectionComponent","dynamic-zone.testimonials-section":"#/components/schemas/DynamicZoneTestimonialsSectionComponent","dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent","dynamic-zone.related-articles":"#/components/schemas/DynamicZoneRelatedArticlesComponent","dynamic-zone.page-hero-section":"#/components/schemas/DynamicZonePageHeroSectionComponent","dynamic-zone.media-text-section":"#/components/schemas/DynamicZoneMediaTextSectionComponent","dynamic-zone.locations-section":"#/components/schemas/DynamicZoneLocationsSectionComponent","dynamic-zone.latest-news-section":"#/components/schemas/DynamicZoneLatestNewsSectionComponent","dynamic-zone.icon-box-section":"#/components/schemas/DynamicZoneIconBoxSectionComponent","dynamic-zone.hover-overlay-card-collection":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent","dynamic-zone.hover-expand-card-collection":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent","dynamic-zone.hero-slide-section":"#/components/schemas/DynamicZoneHeroSlideSectionComponent","dynamic-zone.form-subscribe":"#/components/schemas/DynamicZoneFormSubscribeComponent","dynamic-zone.features":"#/components/schemas/DynamicZoneFeaturesComponent","dynamic-zone.featured-promotions":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent","dynamic-zone.executive-team-section":"#/components/schemas/DynamicZoneExecutiveTeamSectionComponent","dynamic-zone.accordion-section":"#/components/schemas/DynamicZoneAccordionSectionComponent"}}},"seo":{"type":"array","items":{"$ref":"#/components/schemas/SharedSeoComponent"}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"HomeListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Home"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Home":{"type":"object","required":["heading"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"heading":{"type":"string"},"subHeading":{"type":"string"},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneVideoSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneTestimonialsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"},{"$ref":"#/components/schemas/DynamicZoneRelatedArticlesComponent"},{"$ref":"#/components/schemas/DynamicZonePageHeroSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneMediaTextSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneLocationsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneLatestNewsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneIconBoxSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHeroSlideSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneFormSubscribeComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturesComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent"},{"$ref":"#/components/schemas/DynamicZoneExecutiveTeamSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneAccordionSectionComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.video-section":"#/components/schemas/DynamicZoneVideoSectionComponent","dynamic-zone.testimonials-section":"#/components/schemas/DynamicZoneTestimonialsSectionComponent","dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent","dynamic-zone.related-articles":"#/components/schemas/DynamicZoneRelatedArticlesComponent","dynamic-zone.page-hero-section":"#/components/schemas/DynamicZonePageHeroSectionComponent","dynamic-zone.media-text-section":"#/components/schemas/DynamicZoneMediaTextSectionComponent","dynamic-zone.locations-section":"#/components/schemas/DynamicZoneLocationsSectionComponent","dynamic-zone.latest-news-section":"#/components/schemas/DynamicZoneLatestNewsSectionComponent","dynamic-zone.icon-box-section":"#/components/schemas/DynamicZoneIconBoxSectionComponent","dynamic-zone.hover-overlay-card-collection":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent","dynamic-zone.hover-expand-card-collection":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent","dynamic-zone.hero-slide-section":"#/components/schemas/DynamicZoneHeroSlideSectionComponent","dynamic-zone.form-subscribe":"#/components/schemas/DynamicZoneFormSubscribeComponent","dynamic-zone.features":"#/components/schemas/DynamicZoneFeaturesComponent","dynamic-zone.featured-promotions":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent","dynamic-zone.executive-team-section":"#/components/schemas/DynamicZoneExecutiveTeamSectionComponent","dynamic-zone.accordion-section":"#/components/schemas/DynamicZoneAccordionSectionComponent"}}},"seo":{"type":"array","items":{"$ref":"#/components/schemas/SharedSeoComponent"}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"heading":{"type":"string"},"subHeading":{"type":"string"},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneVideoSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneTestimonialsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"},{"$ref":"#/components/schemas/DynamicZoneRelatedArticlesComponent"},{"$ref":"#/components/schemas/DynamicZonePageHeroSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneMediaTextSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneLocationsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneLatestNewsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneIconBoxSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHeroSlideSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneFormSubscribeComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturesComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent"},{"$ref":"#/components/schemas/DynamicZoneExecutiveTeamSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneAccordionSectionComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.video-section":"#/components/schemas/DynamicZoneVideoSectionComponent","dynamic-zone.testimonials-section":"#/components/schemas/DynamicZoneTestimonialsSectionComponent","dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent","dynamic-zone.related-articles":"#/components/schemas/DynamicZoneRelatedArticlesComponent","dynamic-zone.page-hero-section":"#/components/schemas/DynamicZonePageHeroSectionComponent","dynamic-zone.media-text-section":"#/components/schemas/DynamicZoneMediaTextSectionComponent","dynamic-zone.locations-section":"#/components/schemas/DynamicZoneLocationsSectionComponent","dynamic-zone.latest-news-section":"#/components/schemas/DynamicZoneLatestNewsSectionComponent","dynamic-zone.icon-box-section":"#/components/schemas/DynamicZoneIconBoxSectionComponent","dynamic-zone.hover-overlay-card-collection":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent","dynamic-zone.hover-expand-card-collection":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent","dynamic-zone.hero-slide-section":"#/components/schemas/DynamicZoneHeroSlideSectionComponent","dynamic-zone.form-subscribe":"#/components/schemas/DynamicZoneFormSubscribeComponent","dynamic-zone.features":"#/components/schemas/DynamicZoneFeaturesComponent","dynamic-zone.featured-promotions":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent","dynamic-zone.executive-team-section":"#/components/schemas/DynamicZoneExecutiveTeamSectionComponent","dynamic-zone.accordion-section":"#/components/schemas/DynamicZoneAccordionSectionComponent"}}},"seo":{"type":"array","items":{"$ref":"#/components/schemas/SharedSeoComponent"}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"HomeResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Home"},"meta":{"type":"object"}}},"DynamicZoneTestimonialsSectionComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.testimonials-section"]},"adminLabel":{"type":"string"},"styles":{"$ref":"#/components/schemas/StylesSectionStylesComponent"},"items":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"heading":{"$ref":"#/components/schemas/ElementalsHeadingComponent"}}},"DynamicZoneLocationsSectionComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.locations-section"]},"adminLabel":{"type":"string"},"styles":{"$ref":"#/components/schemas/StylesSectionStylesComponent"},"heading":{"$ref":"#/components/schemas/ElementalsHeadingComponent"},"items":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"ItemsHeroSlideItemComponent":{"type":"object","properties":{"id":{"type":"number"},"adminLabel":{"type":"string"},"media":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"heading":{"$ref":"#/components/schemas/ElementalsHeadingComponent"}}},"DynamicZoneHeroSlideSectionComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.hero-slide-section"]},"adminLabel":{"type":"string"},"items":{"type":"array","items":{"$ref":"#/components/schemas/ItemsHeroSlideItemComponent"}}}},"DynamicZoneExecutiveTeamSectionComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.executive-team-section"]},"adminLabel":{"type":"string"},"styles":{"$ref":"#/components/schemas/StylesSectionStylesComponent"},"heading":{"$ref":"#/components/schemas/ElementalsHeadingComponent"},"items":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"SharedUserComponent":{"type":"object","properties":{"id":{"type":"number"},"name":{"type":"string"},"job":{"type":"string"},"image":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}},"LogoRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["image"],"type":"object","properties":{"image":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"company":{"type":"string"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"LogoListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Logo"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Logo":{"type":"object","required":["image"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"image":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"pathId":{"type":"integer"},"parent":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"children":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"files":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"path":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"company":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"image":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"company":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"LogoResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Logo"},"meta":{"type":"object"}}},"PageRequest":{"type":"object","required":["data"],"properties":{"data":{"required":["slug","title"],"type":"object","properties":{"seo":{"$ref":"#/components/schemas/SharedSeoComponent"},"slug":{"type":"string"},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneTestimonialsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneMediaTextSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneLatestNewsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHeroSlideSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneAccordionSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneFormSubscribeComponent"},{"$ref":"#/components/schemas/DynamicZoneVideoSectionComponent"},{"$ref":"#/components/schemas/DynamicZonePageHeroSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneIconBoxSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneExecutiveTeamSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneLocationsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneImageBoxSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneContactUsSectionComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.testimonials-section":"#/components/schemas/DynamicZoneTestimonialsSectionComponent","dynamic-zone.media-text-section":"#/components/schemas/DynamicZoneMediaTextSectionComponent","dynamic-zone.latest-news-section":"#/components/schemas/DynamicZoneLatestNewsSectionComponent","dynamic-zone.hero-slide-section":"#/components/schemas/DynamicZoneHeroSlideSectionComponent","dynamic-zone.accordion-section":"#/components/schemas/DynamicZoneAccordionSectionComponent","dynamic-zone.featured-promotions":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent","dynamic-zone.hover-overlay-card-collection":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent","dynamic-zone.hover-expand-card-collection":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent","dynamic-zone.form-subscribe":"#/components/schemas/DynamicZoneFormSubscribeComponent","dynamic-zone.video-section":"#/components/schemas/DynamicZoneVideoSectionComponent","dynamic-zone.page-hero-section":"#/components/schemas/DynamicZonePageHeroSectionComponent","dynamic-zone.icon-box-section":"#/components/schemas/DynamicZoneIconBoxSectionComponent","dynamic-zone.executive-team-section":"#/components/schemas/DynamicZoneExecutiveTeamSectionComponent","dynamic-zone.locations-section":"#/components/schemas/DynamicZoneLocationsSectionComponent","dynamic-zone.image-box-section":"#/components/schemas/DynamicZoneImageBoxSectionComponent","dynamic-zone.contact-us-section":"#/components/schemas/DynamicZoneContactUsSectionComponent"}}},"adminLabel":{"type":"string"},"title":{"type":"string"},"template":{"type":"string","enum":["dynamic","home-page","blogs-page","products-page"]},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"PageListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Page"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Page":{"type":"object","required":["slug","title"],"properties":{"id":{"type":"number"},"documentId":{"type":"string"},"seo":{"$ref":"#/components/schemas/SharedSeoComponent"},"slug":{"type":"string"},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneTestimonialsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneMediaTextSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneLatestNewsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHeroSlideSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneAccordionSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneFormSubscribeComponent"},{"$ref":"#/components/schemas/DynamicZoneVideoSectionComponent"},{"$ref":"#/components/schemas/DynamicZonePageHeroSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneIconBoxSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneExecutiveTeamSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneLocationsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneImageBoxSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneContactUsSectionComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.testimonials-section":"#/components/schemas/DynamicZoneTestimonialsSectionComponent","dynamic-zone.media-text-section":"#/components/schemas/DynamicZoneMediaTextSectionComponent","dynamic-zone.latest-news-section":"#/components/schemas/DynamicZoneLatestNewsSectionComponent","dynamic-zone.hero-slide-section":"#/components/schemas/DynamicZoneHeroSlideSectionComponent","dynamic-zone.accordion-section":"#/components/schemas/DynamicZoneAccordionSectionComponent","dynamic-zone.featured-promotions":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent","dynamic-zone.hover-overlay-card-collection":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent","dynamic-zone.hover-expand-card-collection":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent","dynamic-zone.form-subscribe":"#/components/schemas/DynamicZoneFormSubscribeComponent","dynamic-zone.video-section":"#/components/schemas/DynamicZoneVideoSectionComponent","dynamic-zone.page-hero-section":"#/components/schemas/DynamicZonePageHeroSectionComponent","dynamic-zone.icon-box-section":"#/components/schemas/DynamicZoneIconBoxSectionComponent","dynamic-zone.executive-team-section":"#/components/schemas/DynamicZoneExecutiveTeamSectionComponent","dynamic-zone.locations-section":"#/components/schemas/DynamicZoneLocationsSectionComponent","dynamic-zone.image-box-section":"#/components/schemas/DynamicZoneImageBoxSectionComponent","dynamic-zone.contact-us-section":"#/components/schemas/DynamicZoneContactUsSectionComponent"}}},"adminLabel":{"type":"string"},"title":{"type":"string"},"template":{"type":"string","enum":["dynamic","home-page","blogs-page","products-page"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"seo":{"$ref":"#/components/schemas/SharedSeoComponent"},"slug":{"type":"string"},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneTestimonialsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneMediaTextSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneLatestNewsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHeroSlideSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneAccordionSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent"},{"$ref":"#/components/schemas/DynamicZoneFormSubscribeComponent"},{"$ref":"#/components/schemas/DynamicZoneVideoSectionComponent"},{"$ref":"#/components/schemas/DynamicZonePageHeroSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneIconBoxSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneExecutiveTeamSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneLocationsSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneImageBoxSectionComponent"},{"$ref":"#/components/schemas/DynamicZoneContactUsSectionComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.testimonials-section":"#/components/schemas/DynamicZoneTestimonialsSectionComponent","dynamic-zone.media-text-section":"#/components/schemas/DynamicZoneMediaTextSectionComponent","dynamic-zone.latest-news-section":"#/components/schemas/DynamicZoneLatestNewsSectionComponent","dynamic-zone.hero-slide-section":"#/components/schemas/DynamicZoneHeroSlideSectionComponent","dynamic-zone.accordion-section":"#/components/schemas/DynamicZoneAccordionSectionComponent","dynamic-zone.featured-promotions":"#/components/schemas/DynamicZoneFeaturedPromotionsComponent","dynamic-zone.hover-overlay-card-collection":"#/components/schemas/DynamicZoneHoverOverlayCardCollectionComponent","dynamic-zone.hover-expand-card-collection":"#/components/schemas/DynamicZoneHoverExpandCardCollectionComponent","dynamic-zone.form-subscribe":"#/components/schemas/DynamicZoneFormSubscribeComponent","dynamic-zone.video-section":"#/components/schemas/DynamicZoneVideoSectionComponent","dynamic-zone.page-hero-section":"#/components/schemas/DynamicZonePageHeroSectionComponent","dynamic-zone.icon-box-section":"#/components/schemas/DynamicZoneIconBoxSectionComponent","dynamic-zone.executive-team-section":"#/components/schemas/DynamicZoneExecutiveTeamSectionComponent","dynamic-zone.locations-section":"#/components/schemas/DynamicZoneLocationsSectionComponent","dynamic-zone.image-box-section":"#/components/schemas/DynamicZoneImageBoxSectionComponent","dynamic-zone.contact-us-section":"#/components/schemas/DynamicZoneContactUsSectionComponent"}}},"adminLabel":{"type":"string"},"title":{"type":"string"},"template":{"type":"string","enum":["dynamic","home-page","blogs-page","products-page"]},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"PageResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Page"},"meta":{"type":"object"}}},"DynamicZoneContactUsSectionComponent":{"type":"object","properties":{"id":{"type":"number"},"__component":{"type":"string","enum":["dynamic-zone.contact-us-section"]},"adminLabel":{"type":"string"},"styles":{"$ref":"#/components/schemas/StylesSectionStylesComponent"},"heading":{"$ref":"#/components/schemas/ElementalsHeadingComponent"},"contact_info":{"type":"array","items":{"$ref":"#/components/schemas/ElementalsIconBoxComponent"}}}},"ProductRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"name":{"type":"string"},"price":{"type":"integer"},"description":{"type":"string"},"slug":{"type":"string"},"images":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"categories":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent"}}},"featured":{"type":"boolean"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"ProductListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Product"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Product":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"price":{"type":"integer"},"description":{"type":"string"},"slug":{"type":"string"},"images":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"pathId":{"type":"integer"},"parent":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"children":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"files":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"path":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"categories":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"product":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"price":{"type":"integer"},"description":{"type":"string"},"slug":{"type":"string"},"images":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"categories":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent"}}},"featured":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent"}}},"featured":{"type":"boolean"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"ProductResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Product"},"meta":{"type":"object"}}},"ProductPageRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"heading":{"type":"string"},"sub_heading":{"type":"string"},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"},{"$ref":"#/components/schemas/DynamicZoneRelatedArticlesComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturesComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent","dynamic-zone.related-articles":"#/components/schemas/DynamicZoneRelatedArticlesComponent","dynamic-zone.features":"#/components/schemas/DynamicZoneFeaturesComponent"}}},"seo":{"$ref":"#/components/schemas/SharedSeoComponent"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"ProductPageListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/ProductPage"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"ProductPage":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"heading":{"type":"string"},"sub_heading":{"type":"string"},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"},{"$ref":"#/components/schemas/DynamicZoneRelatedArticlesComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturesComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent","dynamic-zone.related-articles":"#/components/schemas/DynamicZoneRelatedArticlesComponent","dynamic-zone.features":"#/components/schemas/DynamicZoneFeaturesComponent"}}},"seo":{"$ref":"#/components/schemas/SharedSeoComponent"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"heading":{"type":"string"},"sub_heading":{"type":"string"},"dynamic_zone":{"type":"array","items":{"anyOf":[{"$ref":"#/components/schemas/DynamicZoneRelatedProductsComponent"},{"$ref":"#/components/schemas/DynamicZoneRelatedArticlesComponent"},{"$ref":"#/components/schemas/DynamicZoneFeaturesComponent"}]},"discriminator":{"propertyName":"__component","mapping":{"dynamic-zone.related-products":"#/components/schemas/DynamicZoneRelatedProductsComponent","dynamic-zone.related-articles":"#/components/schemas/DynamicZoneRelatedArticlesComponent","dynamic-zone.features":"#/components/schemas/DynamicZoneFeaturesComponent"}}},"seo":{"$ref":"#/components/schemas/SharedSeoComponent"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"ProductPageResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/ProductPage"},"meta":{"type":"object"}}},"RedirectionRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"source":{"type":"string"},"destination":{"type":"string"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"RedirectionListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Redirection"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Redirection":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"source":{"type":"string"},"destination":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"source":{"type":"string"},"destination":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"RedirectionResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Redirection"},"meta":{"type":"object"}}},"TeamMemberRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"adminLabel":{"type":"string"},"image":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"name_prefix":{"type":"string"},"name":{"type":"string"},"position":{"type":"string"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"TeamMemberListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/TeamMember"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"TeamMember":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"adminLabel":{"type":"string"},"image":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"pathId":{"type":"integer"},"parent":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"children":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"files":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"firstname":{"type":"string"},"lastname":{"type":"string"},"username":{"type":"string"},"email":{"type":"string","format":"email"},"resetPasswordToken":{"type":"string"},"registrationToken":{"type":"string"},"isActive":{"type":"boolean"},"roles":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"code":{"type":"string"},"description":{"type":"string"},"users":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"permissions":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"action":{"type":"string"},"actionParameters":{},"subject":{"type":"string"},"properties":{},"conditions":{},"role":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"blocked":{"type":"boolean"},"preferedLanguage":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}},"path":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"name_prefix":{"type":"string"},"name":{"type":"string"},"position":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"adminLabel":{"type":"string"},"image":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"name_prefix":{"type":"string"},"name":{"type":"string"},"position":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"TeamMemberResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/TeamMember"},"meta":{"type":"object"}}},"TestimonialRequest":{"type":"object","required":["data"],"properties":{"data":{"type":"object","properties":{"text":{"type":"string"},"user":{"$ref":"#/components/schemas/SharedUserComponent"},"image":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"},"locale":{"type":"string"},"localizations":{"type":"array","items":{"oneOf":[{"type":"integer"},{"type":"string"}],"example":"string or id"}}}}}},"TestimonialListResponse":{"type":"object","properties":{"data":{"type":"array","items":{"$ref":"#/components/schemas/Testimonial"}},"meta":{"type":"object","properties":{"pagination":{"type":"object","properties":{"page":{"type":"integer"},"pageSize":{"type":"integer","minimum":25},"pageCount":{"type":"integer","maximum":1},"total":{"type":"integer"}}}}}}},"Testimonial":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"text":{"type":"string"},"user":{"$ref":"#/components/schemas/SharedUserComponent"},"image":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"text":{"type":"string"},"user":{"$ref":"#/components/schemas/SharedUserComponent"},"image":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"integer"},"height":{"type":"integer"},"formats":{},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"float"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{},"related":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}},"folder":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"folderPath":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"},"publishedAt":{"type":"string","format":"date-time"},"createdBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"updatedBy":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}},"locale":{"type":"string"},"localizations":{"type":"array","items":{"type":"object","properties":{"id":{"type":"number"},"documentId":{"type":"string"}}}}}}}}},"TestimonialResponse":{"type":"object","properties":{"data":{"$ref":"#/components/schemas/Testimonial"},"meta":{"type":"object"}}},"UploadFile":{"properties":{"id":{"type":"number"},"name":{"type":"string"},"alternativeText":{"type":"string"},"caption":{"type":"string"},"width":{"type":"number","format":"integer"},"height":{"type":"number","format":"integer"},"formats":{"type":"number"},"hash":{"type":"string"},"ext":{"type":"string"},"mime":{"type":"string"},"size":{"type":"number","format":"double"},"url":{"type":"string"},"previewUrl":{"type":"string"},"provider":{"type":"string"},"provider_metadata":{"type":"object"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"}}},"Users-Permissions-Role":{"type":"object","properties":{"id":{"type":"number"},"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"createdAt":{"type":"string","format":"date-time"},"updatedAt":{"type":"string","format":"date-time"}}},"Users-Permissions-User":{"type":"object","properties":{"id":{"type":"number","example":1},"username":{"type":"string","example":"foo.bar"},"email":{"type":"string","example":"<EMAIL>"},"provider":{"type":"string","example":"local"},"confirmed":{"type":"boolean","example":true},"blocked":{"type":"boolean","example":false},"createdAt":{"type":"string","format":"date-time","example":"2022-06-02T08:32:06.258Z"},"updatedAt":{"type":"string","format":"date-time","example":"2022-06-02T08:32:06.267Z"}}},"Users-Permissions-UserRegistration":{"type":"object","properties":{"jwt":{"type":"string","example":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"},"user":{"$ref":"#/components/schemas/Users-Permissions-User"}}},"Users-Permissions-PermissionsTree":{"type":"object","additionalProperties":{"type":"object","description":"every api","properties":{"controllers":{"description":"every controller of the api","type":"object","additionalProperties":{"type":"object","additionalProperties":{"description":"every action of every controller","type":"object","properties":{"enabled":{"type":"boolean"},"policy":{"type":"string"}}}}}}}}},"requestBodies":{"Users-Permissions-RoleRequest":{"required":true,"content":{"application/json":{"schema":{"type":"object","properties":{"name":{"type":"string"},"description":{"type":"string"},"type":{"type":"string"},"permissions":{"$ref":"#/components/schemas/Users-Permissions-PermissionsTree"}}},"example":{"name":"foo","description":"role foo","permissions":{"api::content-type.content-type":{"controllers":{"controllerA":{"find":{"enabled":true}}}}}}}}}}},"tags":[{"name":"Users-Permissions - Auth","description":"Authentication endpoints","externalDocs":{"description":"Find out more","url":"https://docs.strapi.io/developer-docs/latest/plugins/users-permissions.html"}},{"name":"Users-Permissions - Users & Roles","description":"Users, roles, and permissions endpoints","externalDocs":{"description":"Find out more","url":"https://docs.strapi.io/developer-docs/latest/plugins/users-permissions.html"}}]},
          dom_id: '#swagger-ui',
          docExpansion: "none",
          deepLinking: true,
          presets: [
            SwaggerUIBundle.presets.apis,
            SwaggerUIStandalonePreset,
          ],
          plugins: [
            SwaggerUIBundle.plugins.DownloadUrl,
          ],
          layout: "StandaloneLayout",
        });

        window.ui = ui;
      }
    </script>

    <script src="/plugins/documentation/swagger-ui-bundle.js"></script>
    <script src="/plugins/documentation/swagger-ui-standalone-preset.js"></script>
  </body>
</html>
