import { cn } from '@ttplatform/ui/lib'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'

//----------------------------------------------------------------------------------
type TProps = {
  styles?: any
  children: React.ReactNode
  sectionName?: string
  className?: string
}
//----------------------------------------------------------------------------------
export default function StylesSection({
  styles,
  children,
  sectionName,
  className,
}: TProps) {
  const { background, content_max_width, padding, margin } = styles || {}

  const renderMaxWidth = (maxWidth: string) => {
    switch (maxWidth) {
      case 'full_width':
        return ''
      case 'xl':
        return 'max-w-screen-2xl px-4'
      case 'lg':
        return 'max-w-screen-xl px-4'
      case 'md':
        return 'max-w-screen-lg px-4'
      case 'sm':
        return 'max-w-screen-md px-4'
      case 'xs':
        return 'max-w-screen-sm px-4'
      default:
        return 'max-w-screen-2xl px-4'
    }
  }
  return (
    <section
      data-section={sectionName || ''}
      style={{
        backgroundImage: styles?.background?.image?.url
          ? `url(${RenderImageUrlStrapi({ url: background?.image?.url })})`
          : 'unset',
        backgroundColor: background?.color
          ? `rgb(from ${background?.color} r g b / ${background?.opacity})`
          : '#FFF',

        backgroundPosition: background?.backgroundPosition || 'center center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: background?.size || 'cover',
        backgroundBlendMode: 'multiply',
        position: 'relative',
        paddingTop: padding?.top,
        paddingBottom: padding?.bottom || '0px',
        paddingLeft: padding?.left || '0px',
        paddingRight: padding?.right || '0px',
        marginTop: margin?.top || '0px',
        marginBottom: margin?.bottom || '0px',
        marginLeft: margin?.left || '0px',
        marginRight: margin?.right || '0px',
      }}
      className={cn(className)}
    >
      <div
        className={cn(
          'w-full mx-auto min-[1440px]:max-w-7xl 3xl:max-w-[1600px]',
          renderMaxWidth(content_max_width),
        )}
      >
        {children}
      </div>
    </section>
  )
}
