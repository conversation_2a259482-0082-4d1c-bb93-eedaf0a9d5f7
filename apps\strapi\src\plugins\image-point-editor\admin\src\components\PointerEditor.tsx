import { Box, Button, Field, Flex, JSONInput } from '@strapi/design-system'
import { useField } from '@strapi/strapi/admin'
import type { Schema } from '@strapi/types'
import { FC as FunctionComponent, useState } from 'react'
import FormPointer from './FormPointer'
import MediaLib from './MediaLib'

interface FieldProps {
  name: string
  onChange: (e: { target: { name: string; value: string } }) => void
  value: string
  intlLabel: {
    id: string
    defaultMessage: string
  }
  disabled?: boolean
  error?: string
  description?: {
    id: string
    defaultMessage: string
  }
  required?: boolean
  attribute?: any
  labelAction?: React.ReactNode
}

type Point = {
  _index: number
  id: string
  x: number
  y: number
  title: string
  content: string
  image: any
  buttonText: string
  buttonLink: string
}

type ImageBounds = {
  topLeft: { x: number; y: number }
  topRight: { x: number; y: number }
  bottomLeft: { x: number; y: number }
  bottomRight: { x: number; y: number }
}

type FieldValue = {
  imageUrl: string
  points: Point[]
  bounds: ImageBounds
  markupWidth?: number
  markupHeight?: number
}

const CustomField: FunctionComponent<FieldProps> = ({
  name,
  labelAction,
  required,
  description,
  error,
  intlLabel,
}) => {
  const field = useField(name)
  const formatMessage = (message: { id: string; defaultMessage: string }) =>
    message?.defaultMessage ?? ''

  const [mediaLibVisible, setMediaLibVisible] = useState(false)
  const [points, setPoints] = useState<Point[]>(field?.value?.points || [])

  const handleToggleMediaLib = () => setMediaLibVisible((prev) => !prev)
  const updateFieldValue = (value: Partial<FieldValue>) => {
    field.onChange({
      target: {
        name,
        value: {
          ...field?.value,
          ...value,
        },
      },
    } as React.ChangeEvent<HTMLInputElement>)
  }

  const handleClick = (e: React.PointerEvent<HTMLImageElement>) => {
    e.preventDefault()
    e.stopPropagation()

    const rect = e.currentTarget.getBoundingClientRect()
    const x = ((e.clientX - rect.left) / rect.width) * 100
    const y = ((e.clientY - rect.top) / rect.height) * 100

    const newPoint: Point = {
      _index: points.length,
      id: `point-${points.length + 1}`,
      x,
      y,
      title: '',
      content: '',
      image: '',
      buttonText: '',
      buttonLink: '',
    }

    const updatedPoints = [...points, newPoint]
    setPoints(updatedPoints)
    updateFieldValue({ points: updatedPoints })
  }

  const handleChangeAssets = (assets: Schema.Attribute.MediaValue<true>) => {
    const img = assets[0]
    const { width = 500, height = 500, url } = img

    const markupWidth = 500
    const markupHeight = markupWidth / (width / height)

    const bounds: ImageBounds = {
      topLeft: { x: 0, y: 0 },
      topRight: { x: markupWidth, y: 0 },
      bottomLeft: { x: 0, y: markupHeight },
      bottomRight: { x: markupWidth, y: markupHeight },
    }

    updateFieldValue({
      markupWidth,
      markupHeight,
      imageUrl: url,
      points: [],
      bounds,
    })

    setPoints([])
    handleToggleMediaLib()
  }

  // useEffect(() => {
  //   fetch(`/${PLUGIN_ID}`)
  //     .then((response) => response.json())
  //     .then((data) => {
  //       setConfig(data);
  //     });
  // }, []);

  const handleUpdatePoint = (index: number, data: Point) => {
    const updatedPoints = points.map((p, i) =>
      i === index ? { ...p, ...data } : p,
    )
    setPoints(updatedPoints)
    updateFieldValue({ points: updatedPoints })
  }

  const handleDeletePoint = (index: number) => {
    const updatedPoints = points.filter((_, i) => i !== index)
    setPoints(updatedPoints)
    updateFieldValue({ points: updatedPoints })
  }

  const renderPoint = (point: Point, index: number) => (
    <FormPointer
      key={point.id}
      values={point}
      onSave={(data) => handleUpdatePoint(index, data)}
      onDelete={() => handleDeletePoint(index)}
    >
      <div
        onClick={(e) => e.stopPropagation()}
        style={{
          position: 'absolute',
          top: `${point.y}%`,
          left: `${point.x}%`,
          transform: 'translate(-50%, -50%)',
          width: 20,
          height: 20,
          background: point?.title ? '#4945ff' : 'rgb(73 69 255 / 0.5)',
          border: '1px solid white',
          borderRadius: '50%',
          cursor: 'pointer',
          zIndex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          color: 'white',
          fontWeight: 'bold',
        }}
      >
        {index + 1}
      </div>
    </FormPointer>
  )

  return (
    <div>
      <Field.Root
        name={name}
        id={name}
        error={error}
        hint={description && formatMessage(description)}
      >
        <Flex
          spacing={2}
          alignItems="normal"
          style={{ flexDirection: 'column' }}
        >
          <Field.Label action={labelAction} required={required}>
            {intlLabel ? formatMessage(intlLabel) : name}
          </Field.Label>

          <Box style={{ padding: '20px 0', position: 'relative' }}>
            <Flex direction="column" alignItems="normal">
              <Button onClick={handleToggleMediaLib}>Upload Image</Button>

              {field?.value?.imageUrl && (
                <Box
                  style={{
                    margin: '20px auto',
                    width: '500px',
                    height: `${field?.value?.markupHeight}px`,
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    position: 'relative',
                    backgroundImage: `url(${field?.value?.imageUrl})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    backgroundRepeat: 'no-repeat',
                  }}
                >
                  <div
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'rgba(0, 0, 0, 0.5)',
                      zIndex: 0,
                      cursor: 'crosshair',
                    }}
                    onClick={handleClick}
                  />

                  {points.map((point, index) => renderPoint(point, index))}
                </Box>
              )}
            </Flex>
          </Box>

          <Field.Hint />
          <Field.Error />
        </Flex>

        <MediaLib
          allowedTypes={['images']}
          isOpen={mediaLibVisible}
          onChange={handleChangeAssets}
          onToggle={handleToggleMediaLib}
        />
      </Field.Root>

      <JSONInput
        aria-label="JSON-PREVIEW"
        minHeight="235px"
        value={JSON.stringify(field?.value || {}, null, 2)}
        style={{ width: '100%' }}
      />
    </div>
  )
}

export { CustomField }
