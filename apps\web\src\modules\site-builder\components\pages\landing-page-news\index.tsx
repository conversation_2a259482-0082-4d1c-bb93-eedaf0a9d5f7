'use client'
import { BLandingNewsDetailPage } from '@ttplatform/core-page-builder/components'
import { NewsDetail } from '@ttplatform/core-page-builder/libs'
import { useEffect, useState } from 'react'
import useSWR from 'swr'
export function LandingPageNews({ slug }: { slug: string }) {
  const [newsDetail, setNewsDetail] = useState<NewsDetail | null>(null)
  const fetcher = async (url: string) => {
    const response = await fetch(url, {
      headers: { 'Content-Type': 'application/json' },
    })

    return response.json()
  }
  const { data } = useSWR('/api/posts/landing-news?page=1&limit=100', fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    dedupingInterval: 3600 * 1000,
  })
  useEffect(() => {
    if (data && data.posts) {
      setNewsDetail(data.posts.find((item: any) => item.slug === slug))
    }
  }, [data, slug])
  return <>{newsDetail && <BLandingNewsDetailPage news={newsDetail} />}</>
}
