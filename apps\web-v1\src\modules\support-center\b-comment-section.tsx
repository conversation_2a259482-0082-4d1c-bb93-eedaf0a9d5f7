'use client'

import { useState } from 'react'

import {
  CommentBox,
  CommentData,
  CommentInput,
  SectionTitle,
} from '@ttplatform/core-page-builder/components'
import { Button } from '@ttplatform/ui/components'

const SAMPLE_COMMENTS: CommentData[] = [
  {
    id: '1',
    author: {
      name: '<PERSON>',
      avatarSrc: '/lovable-uploads/e9277b03-9b5b-4f6b-81fd-1d2628a3a950.png',
    },
    content:
      'Office ipsum you must be muted. Company lean well done paradigm. Clean awareness lot shoot could ocean.',
    date: '2025-08-19T10:00:00Z',
    likes: 5,
    replies: [
      {
        id: '1-1',
        author: {
          name: '<PERSON>',
          avatarSrc:
            '/lovable-uploads/e9277b03-9b5b-4f6b-81fd-1d2628a3a950.png',
        },
        content:
          'Space unlock before elephant bandwagon first where best ourselves illustration.',
        date: '2025-08-19T10:30:00Z',
        likes: 3,
        parentId: '1',
        replies: [
          {
            id: '1-1-1',
            author: {
              name: '<PERSON>',
              avatarSrc:
                '/lovable-uploads/e9277b03-9b5b-4f6b-81fd-1d2628a3a950.png',
            },
            content:
              'Respectively hands support building wiggle more angel speed.',
            date: '2025-08-19T11:00:00Z',
            likes: 1,
            parentId: '1-1',
          },
        ],
      },
      {
        id: '1-2',
        author: {
          name: 'Cameron Williamson',
          avatarSrc:
            '/lovable-uploads/e9277b03-9b5b-4f6b-81fd-1d2628a3a950.png',
        },
        content:
          "Don't plane cross activities room expectations whistles stands",
        date: '2025-08-19T12:00:00Z',
        likes: 2,
        parentId: '1',
      },
    ],
  },
  {
    id: '2',
    author: {
      name: 'Ralph Edwards',
      avatarSrc: '/lovable-uploads/e9277b03-9b5b-4f6b-81fd-1d2628a3a950.png',
    },
    content:
      'In mauris porttitor tincidunt mauris massa sit lorem sed scelerisque. Fringilla pharetra vel massa enim sollicitudin cras.',
    date: '2025-08-19T09:30:00Z',
    likes: 5,
  },
  {
    id: '3',
    author: {
      name: 'Ralph Edwards',
      avatarSrc: '/lovable-uploads/e9277b03-9b5b-4f6b-81fd-1d2628a3a950.png',
    },
    content:
      'In mauris porttitor tincidunt mauris massa sit lorem sed scelerisque.',
    date: '2025-08-19T09:00:00Z',
    likes: 5,
    replies: [
      {
        id: '3-1',
        author: {
          name: 'Brooklyn Simmons',
          avatarSrc:
            '/lovable-uploads/e9277b03-9b5b-4f6b-81fd-1d2628a3a950.png',
        },
        content:
          'Fringilla pharetra vel massa enim sollicitudin cras. At pulvinar eget socii',
        date: '2025-08-19T09:15:00Z',
        likes: 4,
        parentId: '3',
      },
    ],
  },
]

interface CommentSectionProps {
  initialComments?: CommentData[]
  title?: string
}

export const BCommentSection = ({
  initialComments = SAMPLE_COMMENTS,
  title = 'BÌNH LUẬN',
}: CommentSectionProps) => {
  const [comments, setComments] = useState<CommentData[]>(initialComments)
  const [visibleComments, setVisibleComments] = useState<number>(3)

  const handleAddComment = (text: string) => {
    const newComment: CommentData = {
      id: `comment-${Date.now()}`,
      author: {
        name: 'Bạn',
        avatarSrc: '/lovable-uploads/e9277b03-9b5b-4f6b-81fd-1d2628a3a950.png',
      },
      content: text,
      date: new Date().toISOString(),
      likes: 0,
    }

    setComments([newComment, ...comments])
  }

  const handleAddReply = (parentId: string, text: string) => {
    const newReply: CommentData = {
      id: `reply-${Date.now()}`,
      author: {
        name: 'Bạn',
        avatarSrc: '/lovable-uploads/e9277b03-9b5b-4f6b-81fd-1d2628a3a950.png',
      },
      content: text,
      date: new Date().toISOString(),
      likes: 0,
      parentId: parentId,
    }

    const addReplyToComment = (commentsList: CommentData[]): CommentData[] => {
      return commentsList.map((comment) => {
        if (comment.id === parentId) {
          return {
            ...comment,
            replies: comment.replies
              ? [...comment.replies, newReply]
              : [newReply],
          }
        } else if (comment.replies && comment.replies.length > 0) {
          return {
            ...comment,
            replies: addReplyToComment(comment.replies),
          }
        }
        return comment
      })
    }

    setComments(addReplyToComment(comments))
  }

  const loadMoreComments = () => {
    setVisibleComments((prevCount) => Math.min(prevCount + 3, comments.length))
  }

  return (
    <div className="w-full mx-auto rounded-lg p-10 bg-gray-50 shadow-sm">
      <SectionTitle text={title} />

      <div className="my-10">
        <CommentInput onSubmit={handleAddComment} />
      </div>

      <div className="space-y-6">
        {comments.slice(0, visibleComments).map((comment) => (
          <CommentBox
            key={comment.id}
            comment={comment}
            onAddReply={handleAddReply}
          />
        ))}
      </div>

      {visibleComments < comments.length && (
        <div className="flex justify-center mt-8">
          <Button
            onClick={loadMoreComments}
            variant="outline"
            className="border-yellow-400 text-black hover:bg-yellow-50"
          >
            TẢI THÊM BÌNH LUẬN
          </Button>
        </div>
      )}
    </div>
  )
}
