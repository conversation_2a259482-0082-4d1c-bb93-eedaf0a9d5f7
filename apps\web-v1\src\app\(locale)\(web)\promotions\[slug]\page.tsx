import { Metadata } from 'next'
import ClientSlugHandler from '~/src/app/(locale)/client-slug-handler'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import { initLingui } from '~/src/localization/i18n-init-lingui'

import { PageBreadcrumb } from '~/src/modules/layout'
import { NewsPromotionsDetail } from '~/src/modules/news-promotions'

export async function generateMetadata(props: {
  params: Promise<{ slug: string }>
}): Promise<Metadata> {
  const params = await props.params

  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.promotions,
    params: {
      filters: {
        slug: params.slug,
        locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = pageData?.seo
  const metadata = generateMetadataObject(seo, pageData?.title)
  return metadata
}

export default async function CareersPage(props: {
  params: Promise<{ slug: string }>
}) {
  const params = await props.params
  const locale = await getLocale()
  const i18n = initLingui(locale)

  const pageData = await fetchContentType({
    contentType: apiRoute.promotions,
    params: {
      'filters[slug][$eq]': params.slug,
      'filters[locale][$eq]': locale,
    },
    spreadData: true,
  })

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = ''
      return acc
    },
    { [locale]: '' },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />

      <PageBreadcrumb
        items={[
          { title: i18n._('Promotions'), href: '/promotions' },
          { title: pageData?.title },
        ]}
      />

      <NewsPromotionsDetail data={pageData} />
    </>
  )
}
