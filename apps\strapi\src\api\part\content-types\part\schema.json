{"kind": "collectionType", "collectionName": "parts", "info": {"singularName": "part", "pluralName": "parts", "displayName": "Product-Parts"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "images": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": true, "allowedTypes": ["images"]}, "banner": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images"]}, "category": {"type": "relation", "relation": "manyToOne", "target": "api::part-category.part-category", "inversedBy": "parts"}, "related_product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "related_parts"}, "dynamic_zone": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["dynamic-zone.info-block", "product-parts.part-hero-section", "product-parts.related-parts"]}, "slug": {"type": "uid", "targetField": "name"}}}