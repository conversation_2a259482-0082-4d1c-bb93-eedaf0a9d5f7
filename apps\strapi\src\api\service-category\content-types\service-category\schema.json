{"kind": "collectionType", "collectionName": "service_categories", "info": {"singularName": "service-category", "pluralName": "service-categories", "displayName": "Service Category"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "name": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}}}