'use client'

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  HexagonIcon,
  LayoutContainer,
  SectionTitle,
} from '@ttplatform/core-page-builder/components'
import { IFAQContent, IFAQItem } from '@ttplatform/core-page-builder/libs'
import { SafeHTML, Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { motion, useInView } from 'framer-motion'
import Image from 'next/image'
import { useRef } from 'react'

// FAQ data structure
const faqData = [
  {
    value: 'faq-1',
    title: '1. Why should I use genuine Cat parts?',
    content: {
      type: 'list',
      items: [
        'Genuine Cat parts improve the life and performance of Cat machines and engines',
        'Cat fluids support contamination control initiatives',
        'Cat fluids adapt to machine and engine design changes',
      ],
    },
  },
  {
    value: 'faq-2',
    title: '2. Are Cat Fluids sustainable?',
    content: {
      type: 'paragraph',
      text: 'Cat Fluids are designed with sustainability in mind, supporting efficient machine performance and reduced environmental impact.',
    },
  },
  {
    value: 'faq-3',
    title: '3. How often should I change Cat Fluids in my equipment?',
    content: {
      type: 'list',
      items: [
        'Change intervals depend on equipment type and usage',
        'Consult your equipment manual for specific recommendations',
        'Regular maintenance schedules improve longevity',
      ],
    },
  },
  {
    value: 'faq.ConcurrentModificationException4',
    title: '4. Best fluids for a specific type of engine?',
    content: {
      type: 'paragraph',
      text: 'The best fluids depend on your engine model and operating conditions. Refer to the manufacturer’s guidelines or consult a Cat dealer.',
    },
  },
  {
    value: 'faq-5',
    title: '5. Best fluids for a specific type of engine?',
    content: {
      type: 'list',
      items: [
        'Use Cat-approved fluids for optimal performance',
        'Check compatibility with your engine model',
        'Ensure fluids meet industry standards',
      ],
    },
  },
  {
    value: 'faq-6',
    title: '6. Best fluids for a specific type of engine?',
    content: {
      type: 'paragraph',
      text: 'Selecting the right fluid involves understanding your engine’s requirements and operating environment. Contact a Cat specialist for tailored advice.',
    },
  },
]

interface BPartsFAQSectionProps {
  title?: {
    active: boolean
    text: string
  }
  accordionItems?: IFAQItem[]
  styles?: {
    column_layout?: {
      default?: string
      lg?: string
    }
    accordion?: {
      default?: string
      trigger?: string
      content?: string
    }
  }
}

export const BPartsFAQSection: React.FC<BPartsFAQSectionProps> = ({
  title = { active: true, text: 'NHỮNG CÂU HỎI THƯỜNG GẶP' },
  accordionItems = faqData,
}) => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: '-100px' })

  // Animation variants
  const accordionVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: (index: number) => ({
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, delay: index * 0.2, ease: 'easeOut' },
    }),
  }

  return (
    <section className={cn('b-faq-section bg-slate-50')}>
      <LayoutContainer>
        {title.active && (
          <SectionTitle text={title.text} underlineWidth="w-32" />
        )}

        <div className={cn('mt-10 grid grid-cols-2 gap-8')} ref={ref}>
          {/* Accordion */}
          <div className="space-y-4">
            {accordionItems.length > 0 ? (
              <Accordion
                type="single"
                collapsible
                defaultValue={accordionItems[0].value}
                className="flex flex-col gap-3"
              >
                {accordionItems.map((item, index) => (
                  <motion.div
                    key={item.value}
                    variants={accordionVariants as any}
                    initial="hidden"
                    animate={isInView ? 'visible' : 'hidden'}
                    custom={index}
                  >
                    <PartnerAccordionItem
                      value={item.value}
                      title={item.title}
                      content={item.content as IFAQContent}
                    />
                  </motion.div>
                ))}
              </Accordion>
            ) : (
              <Typography variant="body1" className="text-center text-gray-500">
                No questions available
              </Typography>
            )}
          </div>

          <div className="relative h-full w-full overflow-hidden rounded-lg">
            <Image
              src="https://res.cloudinary.com/dgbocu1qv/image/upload/v1746960194/j6vd2u5nqrxumvylanke.png"
              alt="CAT 320 GX Excavator with Worker"
              width={500}
              height={500}
              className="object-cover h-full w-full"
              priority
              onError={() => console.error('Image failed to load')}
            />
          </div>
        </div>
      </LayoutContainer>
    </section>
  )
}

interface PartnerAccordionItemProps {
  value: string
  title: string
  content: IFAQContent
}

export function PartnerAccordionItem({
  value,
  title,
  content,
}: PartnerAccordionItemProps) {
  return (
    <AccordionItem
      value={value}
      className="mb-4 rounded-lg border-none bg-white last:mb-0"
    >
      <AccordionTrigger className="h-20 px-5 pl-6 py-3 hover:no-underline cursor-pointer text-black">
        <div className="flex gap-2 items-start">
          <Typography
            variant="body1"
            className="text-left text-base sm:text-lg"
          >
            {title}
          </Typography>
        </div>
      </AccordionTrigger>
      <AccordionContent className={cn('px-5 pl-6 pb-4 text-gray-600')}>
        {content.type === 'paragraph' && (
          <Typography variant="h6" className="font-normal">
            <SafeHTML html={content.text || ''} />
          </Typography>
        )}
        {content.type === 'list' && (
          <ul className="space-y-2">
            {content.items?.map((item, index) => (
              <li key={index} className="flex gap-2 items-start">
                <HexagonIcon size={12} className="mt-0.5 shrink-0" />
                <Typography variant="h6" className="font-normal">
                  <SafeHTML html={item} />
                </Typography>
              </li>
            ))}
          </ul>
        )}
      </AccordionContent>
    </AccordionItem>
  )
}
