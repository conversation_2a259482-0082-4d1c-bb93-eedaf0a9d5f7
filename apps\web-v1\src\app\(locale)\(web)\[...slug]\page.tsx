import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import { PageBreadcrumb } from '~/src/modules/layout'
import ClientSlugHandler from '../../client-slug-handler'
// import ServerSiteHandler from '../../server-site-handler'

// @ts-ignore
export type paramsType = Promise<{ locale: string; slug: string[] }>

type TProps = {
  params: paramsType
}

// @ts-ignore
export async function generateMetadata({ params }: TProps): Promise<Metadata> {
  const { slug } = await params

  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pages,
    params: {
      filters: {
        slug,
        locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = pageData?.seo
  const metadata = generateMetadataObject(seo)
  return metadata
}

export default async function Page({ params }: TProps) {
  const { slug } = await params
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pages,
    params: {
      filters: {
        slug,
        locale,
      },
    },
    spreadData: true,
  })

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = localization.slug

      return acc
    },
    { [locale]: slug },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />

      <PageBreadcrumb items={[{ title: pageData?.title }]} />
      {/* <ServerSiteHandler /> */}
      <PageContent pageData={pageData} />
    </>
  )
}
