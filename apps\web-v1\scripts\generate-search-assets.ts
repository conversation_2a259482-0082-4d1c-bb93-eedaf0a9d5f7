import fs from 'node:fs'
import path from 'node:path'
import axios from 'axios'
import Fuse from 'fuse.js'
import { APP_CONFIG } from '../src/config-global'

// Types
interface StrapiItem {
  documentId?: string
  id?: string | number
  name?: string
  title?: string
  description?: string
  content?: string
  slug?: string
  thumbnail?: {
    url?: string
  }
  image?: {
    url?: string
  }
  tagline?: string
  category?: {
    name?: string
  }
  tags?: Array<{
    name?: string
  }>
}

interface SearchItem {
  documentId: string
  name: string
  description: string
  type: string
  link: string
  thumbnail?: string | null
  priority: number // 1: highest, 2: second, 3: lowest priority for search ordering
  tagline?: string
  category?: string
  tags?: string
}

interface StrapiResponse {
  data?: StrapiItem[]
}

// Configuration

const apiUrl = APP_CONFIG.apiUrl

// Helper function to extract text from rich text objects
function extractTextFromRichText(richText: any[]): string {
  if (!Array.isArray(richText)) return ''

  const extractTextFromNode = (node: any): string => {
    if (!node) return ''

    // If node has text property, return it
    if (typeof node.text === 'string') {
      return node.text
    }

    // If node has children, recursively extract text
    if (Array.isArray(node.children)) {
      return node.children.map(extractTextFromNode).join(' ')
    }

    return ''
  }

  return richText.map(extractTextFromNode).join(' ').trim()
}

const CONTENT_TYPES = [
  // 1. Products (highest priority)
  'products',

  // 2. Promotions & News (second priority)
  'promotions',
  'articles',

  // 3. Other content (lower priority)
  'careers',
  'services',
  // 'support-centers',
] as const

type ContentType = (typeof CONTENT_TYPES)[number]

// Fetch data from Strapi
async function fetchStrapiData(
  contentType: ContentType,
): Promise<StrapiItem[]> {
  try {
    const response = await axios.get<StrapiResponse>(
      `${apiUrl}/api/${contentType}?populate=*`,
    )
    return response.data?.data || []
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error'
    console.error(`Error fetching ${contentType}:`, errorMessage)
    return []
  }
}

// Transform data for search
function transformDataForSearch(
  data: StrapiItem[],
  type: ContentType,
): SearchItem[] {
  // Map content types to their actual routes
  const routeMapping: Record<ContentType, string> = {
    // 1. Products (highest priority)
    products: '/products',

    // 2. Promotions & News (second priority)
    promotions: '/promotions',
    articles: '/news',

    // 3. Other content (lower priority)
    careers: '/careers',
    services: '/services',
    // 'support-centers': '/support-center',
  }

  return data.map((item) => {
    const baseRoute = routeMapping[type] || `/${type}`
    const slug = item.slug || item.documentId || item.id

    // Build link
    const link = `${baseRoute}/${slug}`

    // Handle different field names for different content types
    let name = item.name || item.title || ''
    let description = item.description || item.content || ''

    if (typeof description !== 'string') {
      if (Array.isArray(description)) {
        description = extractTextFromRichText(description)
      } else if (description && typeof description === 'object') {
        description = JSON.stringify(description)
      } else {
        description = String(description || '')
      }
    }

    // Special handling for careers
    if (type === 'careers') {
      name = (item as any).job_title || name
      const jobDetails = []
      if ((item as any).job_location)
        jobDetails.push(`Địa điểm: ${(item as any).job_location}`)
      if ((item as any).job_type)
        jobDetails.push(`Loại hình: ${(item as any).job_type}`)
      if ((item as any).hiring_quantity)
        jobDetails.push(`Số lượng: ${(item as any).hiring_quantity}`)
      if ((item as any).job_department?.title)
        jobDetails.push(`Phòng ban: ${(item as any).job_department.title}`)
      description = `${jobDetails.join(' • ')} • Tuyển dụng việc làm`
    }

    // Add priority weight based on content type
    let priority = 3 // default priority
    if (type === 'products')
      priority = 1 // highest priority
    else if (type === 'promotions' || type === 'articles') priority = 2 // second priority

    return {
      documentId: String(item.documentId || item.id || ''),
      name: name,
      description: description,
      type: type === 'articles' ? 'NEWS' : type.toUpperCase(),
      link: link,
      thumbnail: item.thumbnail?.url || item.image?.url || null,
      priority: priority,
      tagline: item.tagline || '',
      category: item.category?.name || '',
      tags: item.tags?.map((tag) => tag.name).join(' ') || '',
    }
  })
}

// Generate search assets
async function generateSearchAssets(): Promise<void> {
  let allSearchData: SearchItem[] = []

  // Fetch data from all content types
  for (const contentType of CONTENT_TYPES) {
    const data = await fetchStrapiData(contentType)
    const transformedData = transformDataForSearch(data, contentType)
    allSearchData = [...allSearchData, ...transformedData]
  }

  // Sort by priority (1 = highest, 3 = lowest) for better search ranking
  allSearchData.sort((a, b) => a.priority - b.priority)

  // Create Fuse options
  const fuseOptions = {
    keys: [
      { name: 'name', weight: 0.4 },
      { name: 'description', weight: 0.3 },
      { name: 'tagline', weight: 0.2 },
      { name: 'category', weight: 0.1 },
      { name: 'tags', weight: 0.1 },
    ],
    threshold: 0.6, // More lenient for Vietnamese text
    includeScore: true,
    includeMatches: true,
  }

  // Create Fuse instance and generate index
  const fuse = new Fuse(allSearchData, fuseOptions)
  const searchIndex = fuse.getIndex()

  // Ensure public directory exists
  const publicDir = path.join(process.cwd(), 'public')
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true })
  }

  // Write search data and index to files
  const searchDataPath = path.join(publicDir, 'search-data.json')
  const searchIndexPath = path.join(publicDir, 'search-index.json')

  fs.writeFileSync(searchDataPath, JSON.stringify(allSearchData, null, 2))
  fs.writeFileSync(
    searchIndexPath,
    JSON.stringify(searchIndex.toJSON(), null, 2),
  )
}

// Run the script
if (require.main === module) {
  generateSearchAssets().catch((error) => {
    console.error(
      'Stack trace:',
      error instanceof Error ? error.stack : 'No stack trace',
    )
    process.exit(1)
  })
}
