const allowedOrigins = [
  'https://admin.phuthaicat.com.vn/',
  'https://beta.phuthaicat.com.vn/',
  'https://phuthaicat.com.vn',
  // localhost
  'http://localhost:1337',
  'http://localhost:3001',
]

export default [
  'strapi::logger',
  'strapi::errors',
  'strapi::security',
  {
    name: 'strapi::cors',
    config: {
      enabled: true,
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'],
      headers: [
        'Content-Type',
        'Authorization',
        'Origin',
        'Accept',
        'Cache-Control', // Add 'Cache-Control' to the allowed headers
      ],
      origin: allowedOrigins,
    },
  },
  {
    name: 'strapi::poweredBy',
    config: {
      enabled: true,
      poweredBy: 'PTC',
    },
  },
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
  'global::deepPopulate',
]
