import { t } from '@lingui/core/macro'
import { CmsMedia, MainButton } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
//----------------------------------------------------------------------------------

type CardSolutionProps = {
  item: any
}
//----------------------------------------------------------------------------------
export default function CardSolutionCategory({ item }: CardSolutionProps) {
  return (
    <div className="group relative w-full aspect-[2/3] overflow-hidden rounded-xl">
      {/* Background Image */}
      <CmsMedia
        media={item?.banner}
        className="object-cover rounded-xl transition-transform duration-300 w-full h-full group-hover:scale-105"
        fill
        priority
        hoverAnimation={false}
      />

      {/* Gradient Overlay */}
      <div
        style={{
          background:
            'linear-gradient(180deg, rgba(34, 34, 34, 0.00) 0%, #222 100%)',
        }}
        className="absolute bottom-0 z-10 w-full h-1/2"
      />

      {/* Content */}
      <div className="absolute inset-0 flex flex-col justify-end p-8 rounded-xl z-20">
        <Typography variant="h3" className="text-white font-bold uppercase">
          {item?.name}
        </Typography>
        <div className="mt-3 h-0 w-17 bg-yellow-400 group-hover:h-1.5" />
        <div className="sm:overflow-y-auto scrollbar-hide">
          {item?.description && (
            <Typography
              variant="body1"
              className="text-white hidden my-5 max-w-md line-clamp-3 sm:block sm:opacity-0 sm:h-0 sm:my-0 sm:group-hover:opacity-100 sm:group-hover:h-auto sm:group-hover:my-5 transition-all duration-300"
            >
              {item.description}
            </Typography>
          )}
          {item?.slug && (
            <div className="block sm:opacity-0 sm:h-0 sm:group-hover:opacity-100 sm:group-hover:h-auto transition-all duration-300">
              <MainButton
                variant="primary"
                isDisabledIcon={false}
                label={t`View More`}
                url={`/industrial-solutions/${item.slug}`}
                openInNewTab={false}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
