import { APP_CONFIG } from '~/src/config-global'

export const RenderImageUrlStrapi = ({ url }: { url: string }) => {
  if (url) {
    if (url.startsWith('/')) {
      return `${APP_CONFIG.apiUrl}${url}`
    }
    return `${APP_CONFIG.apiUrl}/${url}`
  }
  return '/images/no-image.png'
}
export const RenderLinkUrlStrapi = ({ url }: { url: string }) => {
  if (url) {
    if (url.startsWith('/')) {
      return `${APP_CONFIG.apiUrl}${url}`
    }
    return `${APP_CONFIG.apiUrl}/${url}`
  }
  return '#'
}
