import Image from 'next/image'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'
//

type ImageProps = {
  image: any
}
export default function MediaImage({ image }: ImageProps) {
  return (
    <div className="w-full h-full">
      <Image
        src={RenderImageUrlStrapi({ url: image?.image?.url })}
        alt={image?.image?.alt || 'image hero'}
        width={0}
        height={0}
        sizes="100vw"
        className="w-full h-full object-cover"
        style={{
          borderRadius: `${image?.border_radius || 0}px`,
        }}
      />
    </div>
  )
}
