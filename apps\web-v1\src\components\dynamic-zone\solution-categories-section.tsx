'use client'

import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '@ttplatform/ui/components'
import Autoplay from 'embla-carousel-autoplay'
import { useCallback, useEffect, useRef, useState } from 'react'
import fetchContentTypeClient from '~/src/libs/cms/strapi/fetchContentTypeClient'
import { apiRoute } from '~/src/libs/constants/cms'
import { useLocale } from '~/src/libs/data/use-locale'
import CardContentExpand from '../card/card-content-expand'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
}
//----------------------------------------------------------------------------------
export default function SolutionCategoriesSection({ styles, heading }: TProps) {
  const locale = useLocale()

  const plugin = useRef(Autoplay({ delay: 5000, stopOnInteraction: true }))

  const [solutionCategoriesData, setSolutionCategoriesData] =
    useState<any>(null)

  const fetchSolutionCategoriesData = useCallback(async () => {
    const categoriesData = await fetchContentTypeClient({
      contentType: apiRoute.industrialSolutionCategories,
      params: {
        filters: {
          locale,
        },
      },
    })
    setSolutionCategoriesData(categoriesData?.data)
  }, [locale])

  useEffect(() => {
    fetchSolutionCategoriesData()
  }, [fetchSolutionCategoriesData])

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <div>
        <Carousel
          plugins={[plugin.current]}
          opts={{
            align: 'start',
            loop: true,
          }}
          className="w-full relative"
        >
          <CarouselContent>
            {solutionCategoriesData?.map((item: any, idx: number) => (
              <CarouselItem
                key={idx}
                className="basis-1/1 sm:basis-1/2 md:basis-1/3"
              >
                <CardContentExpand
                  title={item?.name}
                  description={item?.description}
                  image={item?.image}
                  href={`industrial-solutions/${item?.slug}`}
                />
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
    </div>
  )

  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
