'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import {
  FacebookIcon,
  LinkedInIcon,
  MainButton,
  TiktokIcon,
  YouTubeIcon,
} from '@ttplatform/core-page-builder/components'
import {
  T_ContactUsSectionSchema,
  getStrapiImageUrl,
} from '@ttplatform/core-page-builder/libs'
import { Input, Typography } from '@ttplatform/ui/components'
import { motion, useScroll, useSpring, useTransform } from 'framer-motion'
import Image from 'next/image'
import { useRef } from 'react'
import { useForm } from 'react-hook-form'
import { useMediaQuery } from 'usehooks-ts'
import * as z from 'zod'

// Helper function to get background image URL - simple and clean like BTestimonials
const getBackgroundImageUrl = (background?: any): string => {
  if (
    !background?.image ||
    !Array.isArray(background.image) ||
    background.image.length === 0
  ) {
    return '/images/contact-cover.png' // fallback
  }

  const image = background.image[0]
  return getStrapiImageUrl(image, 'large') || '/images/contact-cover.png'
}

// Define the validation schema
const formSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  email: z
    .string()
    .min(1, { message: 'Email is required' })
    .email({ message: 'Invalid email format' }),
})

type FormValues = z.infer<typeof formSchema>

export function BContactBox(props: T_ContactUsSectionSchema = {}) {
  const containerRef = useRef<HTMLDivElement>(null)

  const isMobile = useMediaQuery('(max-width: 767px)')

  const {
    register,
    handleSubmit,
    formState: { errors },
    trigger,
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      name: '',
      email: '',
    },
  })

  // Form submission handler
  const onSubmit = async (data: FormValues) => {
    console.log('Form submitted successfully:', data)
    alert('Form submitted successfully!')
  }

  const onError = (errors: Record<string, { message?: string }>) => {
    const errorMessages: { [key: string]: string } = {}
    Object.keys(errors).forEach((field) => {
      const message = errors[field]?.message || 'Invalid field'
      const errorMsg = typeof message === 'string' ? message : 'Invalid field'

      errorMessages[field] = errorMsg
      trigger(field as keyof FormValues)
    })
  }

  // Animation settings (called unconditionally)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start 100vh', 'end 0vh'],
  })

  const widthProgress = useTransform(
    scrollYProgress,
    [0, 0.3],
    isMobile ? ['100%', '100%'] : ['10%', '100%'],
  )
  const heightProgress = useTransform(
    scrollYProgress,
    [0, 0.5],
    isMobile ? ['100%', '100%'] : ['50%', '100%'],
  )
  const borderRadiusProgress = useTransform(
    scrollYProgress,
    [0, 0.5],
    isMobile ? ['0px', '0px'] : ['400px', '0px'],
  )

  const width = useSpring(widthProgress, { stiffness: 100, damping: 30 })
  const height = useSpring(heightProgress, { stiffness: 100, damping: 30 })
  const borderRadius = useSpring(borderRadiusProgress, {
    stiffness: 100,
    damping: 30,
  })

  const handleButtonClick = () => {
    console.log('Manual validation triggered')
  }

  // Get background image URL and opacity from Strapi or fallback
  const backgroundImageUrl = getBackgroundImageUrl(props.styles?.background)
  const imageOpacity = props.styles?.background?.opacity ?? 1

  const content = (
    <div className="w-full h-full relative">
      <Image
        src={backgroundImageUrl}
        alt="Background image"
        quality={85}
        className="object-cover object-center w-full h-full"
        style={{ opacity: imageOpacity }}
        priority
        width={800}
        height={600}
      />
      <div
        className="absolute inset-0"
        style={{
          backgroundImage:
            'linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7))',
        }}
      />
      <div className="absolute inset-0 flex flex-col items-center justify-center px-4">
        <div className="flex flex-col items-center w-full  lg:max-w-5xl gap-8 md:gap-16 lg:gap-[120px] 3xl:gap-[200px]">
          <div className="flex flex-col items-center gap-10 max-w-[960px] w-full">
            <div className="text-white text-center flex flex-col items-center">
              <Typography
                variant="h2"
                className="mb-4 uppercase"
                style={{ textShadow: '1px 1px 2px rgba(0, 0, 0, 0.5)' }}
              >
                {props.heading?.heading?.text ||
                  'Office ipsum you must be muted effects revision problem into'}
              </Typography>
              <Typography
                variant="body1"
                className="max-w-[800px]"
                style={{ textShadow: '1px 1px 2px rgba(0, 0, 0, 0.5)' }}
              >
                {props.heading?.sub_heading?.text ||
                  'Office ipsum you must be muted. Effects revision problem into. Jumping ocean community playing dangerous speed. Contribution tiger dunder performance model. Can creep algorithm half.'}
              </Typography>
            </div>
            <form
              onSubmit={handleSubmit(onSubmit, onError)}
              className="w-full max-w-[860px] text-left flex flex-col gap-10"
              noValidate
            >
              <div className="flex flex-col md:flex-row items-start space-y-4 md:space-y-0 md:space-x-6 w">
                <div className="w-full md:w-1/2">
                  <Typography variant="caption" className="text-gray-200">
                    Name
                  </Typography>
                  <Input
                    {...register('name')}
                    placeholder="Jane Smith"
                    className="mt-2 w-full bg-white text-black"
                  />
                  {errors.name && (
                    <p className="text-red-400 text-left text-sm mt-1">
                      {errors.name.message}
                    </p>
                  )}
                </div>
                <div className="w-full md:w-1/2">
                  <Typography variant="caption" className="text-gray-200">
                    Email
                  </Typography>
                  <Input
                    {...register('email')}
                    placeholder="<EMAIL>"
                    type="email"
                    className="mt-2 w-full bg-white text-black"
                  />
                  {errors.email && (
                    <p className="text-red-400 text-left text-sm mt-1">
                      {errors.email.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex justify-center">
                {props.heading?.buttons && props.heading.buttons.length > 0 ? (
                  props.heading.buttons.map((button, index: number) => (
                    <MainButton
                      key={index}
                      label={button.text || 'GET STARTED'}
                      variant={(button.variant as any) || 'primary'}
                      url={button.URL}
                      target={button.target as any}
                      type="submit"
                      onClick={handleButtonClick}
                    />
                  ))
                ) : (
                  <MainButton
                    label="GET STARTED"
                    variant="primary"
                    type="submit"
                    onClick={handleButtonClick}
                  />
                )}
              </div>
            </form>
          </div>
          <div
            className="w-full rounded-[16px] px-4 md:px-10 lg:px-20 py-4 md:py-8 lg:py-16 flex flex-col md:flex-row items-center justify-between mx-auto min-h-[120px] md:min-h-[140px] lg:min-h-[162px] max-w-[860px]"
            style={{
              background:
                'linear-gradient(90deg, rgba(255,204,0,1) 0%, rgba(255,211,38,1) 70%, rgba(255,222,89,1) 100%)',
            }}
          >
            <Typography variant="h3" className="text-black mb-4 md:mb-0">
              Kết nối với Phú Thái Cat
            </Typography>
            <div className="flex space-x-4 md:space-x-6 lg:space-x-8 xl:space-x-10 items-center">
              {props.socials && props.socials.length > 0 ? (
                props.socials.map((social) => {
                  switch (social.name?.toLowerCase()) {
                    case 'facebook':
                      return <FacebookIcon key={social.id} />
                    case 'youtube':
                      return <YouTubeIcon key={social.id} />
                    case 'linkedin':
                      return <LinkedInIcon key={social.id} />
                    case 'tiktok':
                      return <TiktokIcon key={social.id} />
                    default:
                      return null
                  }
                })
              ) : (
                <>
                  <FacebookIcon />
                  <YouTubeIcon />
                  <LinkedInIcon />
                  <TiktokIcon />
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="bg-[#fffbf0] w-full">
      <div
        ref={containerRef}
        className="relative  h-screen min-h-screen flex items-center justify-center"
      >
        <motion.div
          style={
            isMobile
              ? { width: '100%', height: '100%', borderRadius: '0px' }
              : { width, height, borderRadius }
          }
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="w-full h-full mx-auto overflow-hidden"
        >
          {content}
        </motion.div>
      </div>
    </div>
  )
}
