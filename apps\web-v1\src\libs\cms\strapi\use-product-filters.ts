import type {
  FilterOption,
  ProductCategoriesResponse,
  ProductCategory,
  ProductSpecificationValuesResponse,
  ProductSpecificationsResponse,
} from '../../types/product'
import { useClientFetch } from './use-client-fetch'

// Utility function to generate slug from name
const generateSlug = (name: string): string => {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
}

/**
 * Hook to fetch products for filter (categories)
 * This replaces useProductTypes since product_types relationship is missing
 */
export const useProductsForFilter = () => {
  const { data, isLoading, error, mutate } = useClientFetch<any>({
    contentType: 'products',
    params: {
      fields: ['id', 'name', 'slug', 'featured'],
      sort: ['name:asc'],
      pagination: {
        pageSize: 100,
      },
      filters: {
        publishedAt: {
          $notNull: true,
        },
      },
    },
  })

  // Transform products to filter options
  const categoryOptions: FilterOption[] =
    data?.data
      ?.filter((product: any) => product.name && product.slug)
      ?.map((product: any) => ({
        id: product.documentId,
        label: product.name,
        value: product.slug,
        count: 0,
      }))
      ?.sort((a: FilterOption, b: FilterOption) => a.label.localeCompare(b.label)) || []

  return {
    products: data?.data || [],
    categoryOptions,
    isLoading,
    error,
    mutate,
  }
}

/**
 * Hook to fetch product types for filter (categories) - DEPRECATED
 * @param productSlug - If provided, only get product types for this specific product
 */
export const useProductTypes = (productSlug?: string) => {
  const { data, isLoading, error, mutate } = useClientFetch<any>({
    contentType: 'product-types',
    params: {
      populate: {
        product: {
          fields: ['id', 'name', 'slug', 'featured'], // Only get essential product fields
        },
      },
      sort: ['name:asc'],
      pagination: {
        pageSize: 100, // Increase to get all types
      },
      filters: {
        // Only get published product types
        publishedAt: {
          $notNull: true,
        },
        // If productSlug is provided, filter by product slug
        ...(productSlug && {
          product: {
            slug: {
              $eq: productSlug,
            },
          },
        }),
      },
    },
  })

  // Transform product types to filter options with better counting
  const categoryOptions: FilterOption[] =
    data?.data
      ?.filter((type: any) => type.name && type.product) // Filter out entries without name or product
      ?.map((type: any) => {
        // Count published products only
        const productCount = Array.isArray(type.product)
          ? type.product.filter((p: any) => p && p.id).length
          : type.product && type.product.id
            ? 1
            : 0

        return {
          id: type.documentId,
          label: type.name,
          value: generateSlug(type.name), // Generate slug from name for filtering
          count: productCount,
        }
      })
      ?.sort((a: FilterOption, b: FilterOption) => {
        // Sort by product count (descending) then by name
        const aCount = a.count || 0
        const bCount = b.count || 0
        if (bCount !== aCount) {
          return bCount - aCount
        }
        return a.label.localeCompare(b.label)
      }) || []

  return {
    productTypes: data?.data || [],
    categoryOptions,
    isLoading,
    error,
    mutate,
  }
}

/**
 * Hook to fetch product categories for filter (deprecated - use useProductTypes)
 */
export const useProductCategories = () => {
  const { data, isLoading, error, mutate } =
    useClientFetch<ProductCategoriesResponse>({
      contentType: 'product-categories',
      params: {
        populate: ['image', 'parent', 'children'],
        sort: ['rank:asc', 'name:asc'],
        pagination: {
          pageSize: 50, // Get all categories
        },
      },
    })

  // Transform categories to filter options
  const categoryOptions: FilterOption[] =
    data?.data?.map((category: ProductCategory) => ({
      id: category.documentId,
      label: category.name,
      value: category.documentId,
      count: category.products?.length || 0,
    })) || []

  return {
    categories: data?.data || [],
    categoryOptions,
    isLoading,
    error,
    mutate,
  }
}

/**
 * Hook to fetch product specifications for filters
 */
export const useProductSpecifications = () => {
  const { data, isLoading, error, mutate } =
    useClientFetch<ProductSpecificationsResponse>({
      contentType: 'product-specifications',
      params: {
        populate: ['parent', 'children', 'product_specification_values'],
        sort: ['rank:asc', 'name:asc'],
        pagination: {
          pageSize: 50,
        },
      },
    })

  return {
    specifications: data?.data || [],
    isLoading,
    error,
    mutate,
  }
}

/**
 * Hook to fetch product specification values
 */
export const useProductSpecificationValues = (specificationId?: string) => {
  const { data, isLoading, error, mutate } =
    useClientFetch<ProductSpecificationValuesResponse>({
      contentType: 'product-specification-values',
      params: {
        populate: ['product_specification', 'product', 'product_model'],
        ...(specificationId && {
          filters: {
            product_specification: {
              documentId: {
                $eq: specificationId,
              },
            },
          },
        }),
        sort: ['value:asc'],
        pagination: {
          pageSize: 50, // Get all values
        },
      },
    })

  return {
    values: data?.data || [],
    isLoading,
    error,
    mutate,
  }
}

/**
 * Hook to get all specification values for dynamic filters
 */
export const useAllSpecificationValues = () => {
  const { data, isLoading, error, mutate } =
    useClientFetch<ProductSpecificationValuesResponse>({
      contentType: 'product-specification-values',
      params: {
        populate: ['product_specification'],
        sort: ['value:asc'],
        pagination: {
          pageSize: 50, // Get all values
        },
      },
    })

  return {
    values: data?.data || [],
    isLoading,
    error,
    mutate,
  }
}

/**
 * Hook to get dynamic filter specifications
 */
export const useFilterData = () => {
  const { specifications, isLoading: specsLoading } = useProductSpecifications()
  const { values: allValues, isLoading: valuesLoading } =
    useAllSpecificationValues()

  // Process specifications into filter-ready format
  const processedSpecs = specifications.map((spec) => {
    // Get values for this specification
    const specValues = allValues.filter(
      (value) => value.product_specification?.documentId === spec.documentId,
    )

    // Options filter for categorical values
    const options = specValues.map((value) => ({
      id: value.documentId,
      label: value.value,
      value: value.value,
      count: 0,
    }))

    return {
      id: spec.documentId,
      name: spec.name,
      slug: spec.name.toLowerCase().replace(/\s+/g, '-'),
      type: 'options' as const,
      options,
      isLoading: false,
    }
  })

  const isLoading = specsLoading || valuesLoading

  return {
    specifications: processedSpecs,
    isLoading,
  }
}
