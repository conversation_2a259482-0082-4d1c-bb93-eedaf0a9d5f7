import type { LinguiConfig } from "@lingui/conf";

const locales = process.env.NEXT_PUBLIC_LANGUAGES?.split(',') || ['en', 'vi']
const defaultLocale = process.env.NEXT_PUBLIC_DEFAULT_LOCALE || 'en'

/** @type {import('@lingui/conf').LinguiConfig} */
const config: LinguiConfig = {
  locales,
  pseudoLocale: 'pseudo',
  sourceLocale: defaultLocale,
  catalogs: [
    {
      path: '<rootDir>/src/locales/{locale}/messages',
      include: ['src'],
    },
  ],
  fallbackLocales: {
    default: defaultLocale,
  },
}
export default config
