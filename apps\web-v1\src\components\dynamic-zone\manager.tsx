'use client'

import React from 'react'

import { BCommitment } from '@ttplatform/core-page-builder/components'

import GeneratorSelectionGuide from './generator-selection-guide'
import {
  AccordionSection,
  BHtml,
  CareersList,
  ContactBranchesSection,
  ContactUsSection,
  DistributedBrandsSection,
  FeaturesSection,
  FeedbackSection,
  FormSubscriber,
  HeroSlider,
  HoverExpandCardSection,
  HoverOverlayCardSection,
  IconBoxListSection,
  ImageSliderSection,
  InfoBlock,
  MediaTextSection,
  NewsFeatured,
  NewsListing,
  NewsRelated,
  NewsSection,
  OtherServicesSection,
  PageHeroSection,
  PartnerCommitmentSection,
  ProductCategoriesSection,
  ProductComparison,
  ProductDetailsSection,
  ProductMediaGallery,
  ProductPartCategories,
  ProductRelatedParts,
  ProductRelatedServices,
  ProductsByCategory,
  PromotionFeatured,
  PromotionListing,
  PromotionsSection,
  RecentlyViewedProducts,
  RelatedIndustries,
  RelatedProductCategories,
  RelatedProductsSection,
  RelatedSolutionsSection,
  ServiceListSection,
  SolutionCategoriesSection,
  SolutionMediaGallery,
  SolutionPortfolioSection,
  StaffMemberSection,
  StatsSection,
  StepGuideSection,
  SupportCenterSection,
  TimelineSection,
  VideoSection,
  WidgetSection,
} from './index'

interface DynamicZoneComponent {
  __component: string
  id: number
  [key: string]: any
}

interface Props {
  dynamicZone: DynamicZoneComponent[]
  locale: string
  banner_ads?: any
}

const componentMapping: { [key: string]: any } = {
  //global
  'dynamic-zone.info-block': InfoBlock,
  'dynamic-zone.features-section': FeaturesSection,
  // Home
  'dynamic-zone.hero-slide-section': HeroSlider,
  'dynamic-zone.hover-overlay-card-collection': HoverOverlayCardSection,
  'dynamic-zone.partners-commitment-section': PartnerCommitmentSection,
  'dynamic-zone.accordion-section': AccordionSection,
  'dynamic-zone.hover-expand-card-collection': HoverExpandCardSection,
  'dynamic-zone.testimonials-section': FeedbackSection,
  'dynamic-zone.latest-news-section': NewsSection,
  'dynamic-zone.featured-promotions': PromotionsSection,
  'dynamic-zone.form-subscribe': FormSubscriber,
  'dynamic-zone.commitment-section': BCommitment,
  // 'dynamic-zone.company-intro': BCompanyIntro,

  // About us
  'dynamic-zone.page-hero-section': PageHeroSection,
  'dynamic-zone.image-slider-section': ImageSliderSection,
  'dynamic-zone.media-text-section': MediaTextSection,
  'dynamic-zone.video-section': VideoSection,
  'dynamic-zone.icon-box-list-section': IconBoxListSection,
  'dynamic-zone.locations-section': ContactBranchesSection,
  'dynamic-zone.timeline-section': TimelineSection,
  'dynamic-zone.executive-team-section': StaffMemberSection,
  'dynamic-zone.stats-section': StatsSection,
  'dynamic-zone.distributed-brands': DistributedBrandsSection,

  'dynamic-zone.html': BHtml,
  // Contact us
  'dynamic-zone.contact-us-section': ContactUsSection,
  // Products
  'dynamic-zone.product-categories-section': ProductCategoriesSection,
  'dynamic-zone.product-media-gallery': ProductMediaGallery,
  'dynamic-zone.products-by-category': ProductsByCategory,
  'dynamic-zone.product-related-services': ProductRelatedServices,
  'dynamic-zone.recently-viewed-products': RecentlyViewedProducts,
  'dynamic-zone.product-related-parts': ProductRelatedParts,
  'dynamic-zone.product-comparison': ProductComparison,
  'dynamic-zone.product-details-section': ProductDetailsSection,
  // Parts
  'product-parts.product-part-categories': ProductPartCategories,
  // Services
  'dynamic-zone.service-list-section': ServiceListSection,
  'dynamic-zone.other-services-section': OtherServicesSection,
  'dynamic-zone.solution-portfolio-section': SolutionPortfolioSection,
  'products.generator-selection-guide': GeneratorSelectionGuide,
  'dynamic-zone.related-products': RelatedProductsSection,
  'dynamic-zone.news-related': NewsRelated,
  // Solutions
  'dynamic-zone.solution-categories-section': SolutionCategoriesSection,
  'dynamic-zone.related-solutions-section': RelatedSolutionsSection,
  'dynamic-zone.related-industries': RelatedIndustries,
  'dynamic-zone.related-product-categories': RelatedProductCategories,
  'dynamic-zone.solution-media-gallery': SolutionMediaGallery,
  // Career
  'dynamic-zone.job-listings-section': CareersList,

  // News + Promotions
  'dynamic-zone.news-featured-section': NewsFeatured,
  'dynamic-zone.news-listing-section': NewsListing,
  'dynamic-zone.promotions-featured-section': PromotionFeatured,
  'dynamic-zone.promotions-listing-section': PromotionListing,
  // Support Center
  'dynamic-zone.support-center-section': SupportCenterSection,

  // Helpers
  'dynamic-zone.widget': WidgetSection,
  // Parts.cat.com
  'dynamic-zone.step-guide-section': StepGuideSection,
}

const convertToDynamicZoneTogetherZone = (dynamicZone: any[]): any[] => {
  const processComponents = (
    startIndex: number,
    parentId?: string,
  ): [any[], number] => {
    const result = []
    let i = startIndex
    while (i < dynamicZone.length) {
      const current = dynamicZone[i]

      // Skip components that belong to a different parent
      if (current.parentId && !parentId) {
        i++
        continue
      }

      if (current.__component === 'dynamic-zone.html') {
        if (current.is_open === true) {
          // Process nested components recursively
          const [children, nextIndex] = processComponents(i + 1, current.id)

          const newZone = {
            __component: 'dynamic-zone.html',
            id: current.id,
            open: true,
            content: current.content,
            children: children,
          }
          result.push(newZone)
          i = nextIndex
        } else if (current.is_open === false && parentId) {
          // End of nested section
          return [result, i + 1]
        } else {
          result.push(current)
          i++
        }
      } else {
        result.push({
          ...current,
          parentId: parentId || current.parentId,
        })
        i++
      }
    }

    return [result, i]
  }

  const [processedZones] = processComponents(0)
  return processedZones
}

const DynamicZoneManager: React.FC<Props> = ({
  dynamicZone,
  locale,
  banner_ads,
}) => {
  console.log('🚀 ~ dynamicZone:', dynamicZone)
  const newDynamicZone = convertToDynamicZoneTogetherZone(dynamicZone)

  const renderComponent = (componentData: any, index: number) => {
    try {
      const Component = componentMapping[componentData?.__component]

      if (!Component) {
        console.warn(`No component found for: ${componentData.__component}`)
        return null
      }

      if (componentData.__component === 'dynamic-zone.html') {
        return (
          <BHtml key={index} {...componentData} locale={locale}>
            {componentData.children.map(renderComponent)}
          </BHtml>
        )
      }

      return (
        <Component
          key={index}
          {...componentData}
          locale={locale}
          banner_ads={banner_ads}
        />
      )
    } catch (error) {
      console.error(
        `Error rendering component: ${componentData.__component}`,
        error,
      )
      return null
    }
  }

  return <>{newDynamicZone.map(renderComponent)}</>
}

export default DynamicZoneManager

// #########################################################
