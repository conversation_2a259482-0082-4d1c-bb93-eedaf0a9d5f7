'use client'

import { MainButton } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import Image from 'next/image'
// import { useParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react'
import fetchContentTypeClient from '~/src/libs/cms/strapi/fetchContentTypeClient'
import { useLocale } from '~/src/libs/data/use-locale'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
}
//----------------------------------------------------------------------------------
export default function ProductCategoriesSection({ styles, heading }: TProps) {
  const locale = useLocale()

  const [productCategoriesData, setProductCategoriesData] = useState<any>(null)

  const fetchProductCategoriesData = useCallback(async () => {
    const productCategories = await fetchContentTypeClient({
      contentType: 'product-categories',
      params: {
        filters: {
          rank: 1,
          locale,
        },
      },
    })
    setProductCategoriesData(productCategories?.data)
  }, [locale])

  useEffect(() => {
    fetchProductCategoriesData()
  }, [fetchProductCategoriesData])

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <div
        className="flex flex-col p-5 sm:p-10 lg:p-20 gap-8 md:gap-10 lg:gap-16 rounded-2xl bg-[#FAFAFA]"
        style={{
          boxShadow:
            '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
        }}
      >
        {productCategoriesData?.map((category: any, idx: number) => (
          <CategoryItem key={idx} category={category} />
        ))}
      </div>
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}

function CategoryItem({ category }: { category: any }) {
  return (
    <div className="flex flex-col md:flex-row items-center gap-x-10 group md:odd:flex-row-reverse">
      <div className="w-full md:w-1/2 flex flex-col gap-5 md:gap-8">
        <Image
          src={'/icons/icon-hexagon.svg'}
          alt="category icon"
          width={0}
          height={0}
          sizes="100vw"
          className="w-8 md:w-10 lg:w-12 h-auto"
        />
        <div className="block md:hidden w-full h-auto aspect-[4/3] relative">
          <div
            className="w-full h-full absolute top-0 left-0 rounded-2xl group-hover:scale-105 md:group-hover:scale-110 ease-in-out transition-all duration-500"
            style={{
              backgroundImage: `url(${RenderImageUrlStrapi({ url: category?.image?.url })})`,
              backgroundRepeat: 'no-repeat',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          ></div>
        </div>
        <div className="flex flex-col gap-4">
          <Typography
            variant={'h2'}
            className="relative pb-2 text-gray-800 uppercase before:absolute before:bottom-0  before:content-[''] before:w-[80px] before:h-[8px] before:bg-[#FFCC00] before:left-0"
          >
            {category?.name}
          </Typography>
          {category?.excerpt ? (
            <Typography variant={'body1'} className="text-gray-700 m-0">
              {category?.excerpt}
            </Typography>
          ) : null}
        </div>
        <MainButton
          variant="secondary"
          isDisabledIcon={false}
          label={'Xem chi tiết'}
          url={'#'}
        />
      </div>
      <div className="hidden md:block md:w-1/2 h-auto aspect-[700/466] relative">
        <div
          className="w-full h-full absolute top-0 left-0 rounded-2xl group-hover:scale-110 ease-in-out transition-all duration-500"
          style={{
            backgroundImage: `url(${RenderImageUrlStrapi({ url: category?.image?.url })})`,
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        ></div>
      </div>
    </div>
  )
}
