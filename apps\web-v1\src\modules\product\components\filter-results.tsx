import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { SectionTitle } from '@ttplatform/core-page-builder/components'
import {
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Typography,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { useCallback, useMemo } from 'react'

import { HexagonButton } from '@ttplatform/core-page-builder/components'
import { CardProduct } from '~/src/components/card/card-product'
import {
  useGetProductModels,
  useGetProducts,
} from '~/src/libs/cms/strapi/use-products'
import { ViewModeToggle } from './view-mode-toggle'
//---------------------------------------------------------------------------------
const __LIST_PRODUCT = [
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
]

const _TABLE_HEAD = [
  {
    name: 'Compare',
    className: 'max-w-max p-0',
  },
  {
    name: 'Model',
    className: 'w-[40%] md:w-[50%] p-0',
  },
  {
    name: 'Effective Power',
    className: 'w-[40%] md:w-[50%] p-0',
  },
  {
    name: 'Bucket Capacity',
    className: 'w-[40%] md:w-[50%] p-0',
  },
  {
    name: 'Operating Weight',
    className: 'w-[40%] md:w-[50%] p-0',
  },
]
//---------------------------------------------------------------------------------
type IProps = {
  filters: any
  onFilters: (name: string, value: any) => void
  productSlug?: string // Optional product slug to filter by specific product
}
export default function FilterResults({
  filters,
  onFilters,
  productSlug,
}: IProps) {
  // Fetch products (filtered by productSlug if provided)
  const { data: productsData } = useGetProducts({
    populate: {
      product_types: {
        fields: ['id', 'name', 'documentId'],
      },
    },
    filters: productSlug
      ? {
        slug: { $eq: productSlug },
      }
      : undefined,
    pagination: {
      pageSize: 100,
    },
  })

  // Fetch product-models (filtered by productSlug if provided)
  const { data: productModelsData, isLoading } = useGetProductModels({
    populate: {
      product: {
        fields: ['id', 'name', 'slug'],
      },
      images: {
        fields: ['url', 'alternativeText'],
      },
    },
    filters: productSlug
      ? {
        product: {
          slug: { $eq: productSlug },
        },
      }
      : undefined,
    pagination: {
      pageSize: 100,
    },
  })

  const handleChangeDisplayMode = useCallback(
    (value: string) => {
      onFilters('displayMode', value)
    },
    [onFilters],
  )

  // 2-step filtering: Filter → Product-types → Products → Product-models
  const filteredProducts = useMemo(() => {
    if (!productModelsData?.data || !productsData?.data) return []

    const productModels = productModelsData.data
    const products = productsData.data
    const selectedCategories = filters?.category || []

    // If no categories selected, return all product models
    if (selectedCategories.length === 0) {
      return productModels.map((productModel: any) => ({
        ...productModel,
        // Map images array to single image object that CardProduct expects
        image: productModel.images?.[0] ? {
          url: productModel.images[0].url,
          alternativeText: productModel.images[0].alternativeText
        } : null,
      }))
    }

    // Step 1: Find products that have the selected product-types (by documentId)
    const matchingProductSlugs = products
      .filter((product: any) => {
        const productTypes = product.product_types || []
        return productTypes.some((type: any) =>
          selectedCategories.includes(type.documentId),
        )
      })
      .map((product: any) => product.slug)

    // Step 2: Filter product-models that belong to those products
    const filtered = productModels.filter((productModel: any) => {
      const productSlug = productModel.product?.slug
      return productSlug && matchingProductSlugs.includes(productSlug)
    })

    return filtered.map((productModel: any) => ({
      ...productModel,
      // Map images array to single image object that CardProduct expects
      image: productModel.images?.[0] ? {
        url: productModel.images[0].url,
        alternativeText: productModel.images[0].alternativeText
      } : null,
    }))
  }, [productModelsData?.data, productsData?.data, filters?.category])

  const renderDisplayMode = (
    <div className="flex items-center gap-4 whitespace-nowrap">
      <Typography variant="body2" className="text-gray-700 font-medium">
        <Trans>Display Mode:</Trans>
      </Typography>
      <ViewModeToggle
        value={filters?.displayMode}
        onChange={handleChangeDisplayMode}
      />
    </div>
  )

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <SectionTitle text={t`PRODUCT MODELS`} align="left" />
        {renderDisplayMode}
      </div>
      {/* Grid */}
      {filters?.displayMode === 'grid' ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-10">
          {isLoading ? (
            <div className="col-span-full text-center py-8">
              <Typography variant="body2" className="text-gray-500">
                <Trans>Loading products...</Trans>
              </Typography>
            </div>
          ) : filteredProducts.length === 0 ? (
            <div className="col-span-full text-center py-8">
              <Typography variant="body2" className="text-gray-500">
                <Trans>No products found for selected filters.</Trans>
              </Typography>
            </div>
          ) : (
            filteredProducts.map((item: any, idx: number) => (
              <CardProduct key={item.documentId || idx} product={item} />
            ))
          )}
        </div>
      ) : null}
      {/* Table */}
      {filters?.displayMode === 'table' ? (
        <Table className="border rounded-2xl">
          <TableHeader className="bg-[#FFCC00]">
            <TableRow>
              {_TABLE_HEAD.map((item: any, idx: number) => (
                <TableHead key={idx} className={cn('p-0', item?.className)}>
                  <Typography
                    variant={'body2'}
                    className="text-[#182230] font-medium px-4 md:px-6 py-3"
                  >
                    <Trans>{item?.name}</Trans>
                  </Typography>
                </TableHead>
              ))}
              <TableHead className="w-[40%] md:w-[50%] p-0"></TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  <Typography variant="body2" className="text-gray-500">
                    <Trans>Loading products...</Trans>
                  </Typography>
                </TableCell>
              </TableRow>
            ) : filteredProducts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  <Typography variant="body2" className="text-gray-500">
                    <Trans>No products found for selected filters.</Trans>
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              filteredProducts.map((item: any, idx: number) => (
                <TableRow key={item.documentId || idx}>
                  <TableCell className="px-4 md:px-6 py-3 md:py-4">
                    <Checkbox className="data-[state=checked]:text-black" />
                  </TableCell>
                  <TableCell className="px-4 md:px-6 py-3 md:py-4">
                    <Typography variant={'body2'} className="text-gray-800">
                      {item?.name}
                    </Typography>
                  </TableCell>
                  <TableCell className="px-4 md:px-6 py-3 md:py-4">
                    <Typography variant={'body2'} className="text-gray-800">
                      {item?.cong_suat}
                    </Typography>
                  </TableCell>
                  <TableCell className="px-4 md:px-6 py-3 md:py-4">
                    <Typography variant={'body2'} className="text-gray-800">
                      {item?.dung_tich_gau}
                    </Typography>
                  </TableCell>
                  <TableCell className="px-4 md:px-6 py-3 md:py-4">
                    <Typography variant={'body2'} className="text-gray-800">
                      {item?.khoi_luong_van_hanh}
                    </Typography>
                  </TableCell>
                  <TableCell className="px-4 md:px-6 py-3 md:py-4">
                    <HexagonButton />
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      ) : null}
    </div>
  )
}
