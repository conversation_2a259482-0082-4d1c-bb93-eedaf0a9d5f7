import { cmsContentType } from '../../constants/cms'
import { QueryParams, useClientFetch } from './use-client-fetch'

const CNT_WIDGETS = cmsContentType.widgets

export const useGetWidgets = ({ filters, locale, status }: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_WIDGETS,
    params: {
      filters,
      locale,
      status,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}
