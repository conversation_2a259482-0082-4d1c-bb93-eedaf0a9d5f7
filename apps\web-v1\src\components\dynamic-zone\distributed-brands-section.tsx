import { MainButton } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import Image from 'next/image'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
  brands: any
}
//----------------------------------------------------------------------------------
export default function DistributedBrandsSection({
  styles,
  heading,
  brands,
}: TProps) {
  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-6">
        {brands.map((brand: any, idx: number) => (
          <BrandItem key={idx} brand={brand} />
        ))}
      </div>
    </div>
  )

  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}

function BrandItem({ brand }: { brand: any }) {
  return (
    <div
      className="w-full h-full flex flex-col group border border-gray-200 rounded-lg"
      style={{
        boxShadow:
          '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
      }}
    >
      <div className="w-full h-auto flex items-center justify-center overflow-hidden rounded-lg">
        <Image
          src={RenderImageUrlStrapi({ url: brand?.logo?.url })}
          alt={`logo-${brand?.name}` || 'logo brand'}
          width={0}
          height={0}
          sizes="100vw"
          className="w-full h-auto object-cover"
        />
      </div>
      <div className="flex flex-col gap-2 p-4 md:p-6">
        <div className="flex items-center justify-between gap-4">
          <Typography
            variant="h6"
            weight="bold"
            color="gray-800"
            className="capitalize"
          >
            {brand?.name}
          </Typography>

          <MainButton
            variant="primary"
            isDisabledIcon={false}
            // label={'XEM THÊM'}
            url={`/about/${brand?.slug}`}
            openInNewTab={false}
          />
        </div>
        <Typography
          variant="body2"
          weight="normal"
          color="gray-700"
          className="line-clamp-3"
        >
          {brand?.description}
        </Typography>
      </div>
    </div>
  )
}
