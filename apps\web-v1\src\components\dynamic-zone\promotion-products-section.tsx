'use client'
import { Button, Typography } from '@ttplatform/ui/components'
import { EmptyContent } from '@ttplatform/ui/templates'
import { useState } from 'react'
import { CardProduct } from '../card/card-product'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
  products: any
}
//----------------------------------------------------------------------------------
export default function PromotionProductsSection({
  styles,
  heading,
  products,
}: TProps) {
  const [limit, setLimit] = useState(3)
  const handleViewMore = () => {
    setLimit((prev) => prev + 3)
  }
  const visibleProducts = products?.slice(0, limit) || []
  const hasMore = limit < products?.length
  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {visibleProducts.length > 0 ? (
          visibleProducts?.map((item: any, idx: number) => (
            <CardProduct key={idx} product={item} />
          ))
        ) : (
          <EmptyContent title="Không có sản phẩm" className="mx-auto" />
        )}
      </div>
      {visibleProducts.length > 0 && hasMore && (
        <Button
          onClick={handleViewMore}
          className="bg-yellow-400 w-[170px] h-auto mx-auto"
        >
          <Typography variant="body1" className="text-[#182230]">
            Xem thêm
          </Typography>
        </Button>
      )}
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
