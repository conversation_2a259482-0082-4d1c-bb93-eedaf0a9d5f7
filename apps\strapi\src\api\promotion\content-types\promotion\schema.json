{"kind": "collectionType", "collectionName": "promotions", "info": {"singularName": "promotion", "pluralName": "promotions", "displayName": "Promotions"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "title": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "targetField": "title"}, "featured": {"type": "boolean", "pluginOptions": {"i18n": {"localized": true}}}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "image": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images"]}, "content": {"type": "blocks", "pluginOptions": {"i18n": {"localized": true}}}, "valid_until": {"type": "date", "pluginOptions": {"i18n": {"localized": true}}}, "category": {"type": "relation", "relation": "manyToOne", "target": "api::promotion-category.promotion-category", "inversedBy": "promotions"}, "related_products": {"type": "relation", "relation": "oneToMany", "target": "api::product.product", "mappedBy": "related_promotion"}, "dynamic_zone": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["dynamic-zone.related-products", "dynamic-zone.latest-promotions-section", "dynamic-zone.video-section", "elementals.media-image", "elementals.media-video", "elementals.image-slider"]}, "seo": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "shared.seo", "repeatable": false}}}