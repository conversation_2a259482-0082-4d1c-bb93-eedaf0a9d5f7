import { Metadata } from 'next'
import ClientSlugHandler from '~/src/app/(locale)/client-slug-handler'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import { PageBreadcrumb } from '~/src/modules/layout'
//----------------------------------------------------------------------------------
export async function generateMetadata(props: {
  params: Promise<{ slug: string }>
}): Promise<Metadata> {
  const params = await props.params

  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.promotions,
    params: {
      filters: {
        slug: params.slug,
        locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = pageData?.seo
  const metadata = generateMetadataObject(seo)
  return metadata
}

export default async function AboutBrandPage(props: {
  params: Promise<{ slug: string }>
}) {
  const params = await props.params

  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.brands,
    params: {
      'filters[slug][$eq]': params.slug,
      'filters[locale][$eq]': locale,
    },
    spreadData: true,
  })
  console.log('🚀 ~ pageData:', pageData)

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = ''
      return acc
    },
    { [locale]: '' },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />

      <PageBreadcrumb
        items={[
          { title: 'Giới thiệu', href: '/about' },
          { title: pageData?.name },
        ]}
      />

      {/* <AboutBrandView data={pageData} /> */}
      <PageContent pageData={pageData} />
    </>
  )
}
