import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'

import { apiRoute } from '~/src/libs/constants/cms'

import { Metadata } from 'next'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import { getLocale } from '~/src/libs/data/cookies'
import { initLingui } from '~/src/localization/i18n-init-lingui'
import { PageBreadcrumb } from '~/src/modules/layout'
import ClientSlugHandler from '../../../client-slug-handler'
// import ServerSiteHandler from '../../../server-site-handler'

// import ServiceDetailsView from '~/src/modules/service/service-details-view'
export type paramsType = Promise<{ locale: string; slug: string[] }>
type TProps = {
  params: paramsType
}
//----------------------------------------------------------------------
export async function generateMetadata({ params }: TProps): Promise<Metadata> {
  const { slug } = await params

  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pages,
    params: {
      filters: {
        slug,
        locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = pageData?.seo
  const metadata = generateMetadataObject(seo, pageData?.title)
  return metadata
}
//----------------------------------------------------------------------

export default async function ServiceDetailsPage({ params }: TProps) {
  const { slug } = await params

  const locale = await getLocale()
  const i18n = initLingui(locale)

  const pageData = await fetchContentType({
    contentType: apiRoute.services,
    params: {
      filters: {
        slug,
        locale,
      },
    },
    spreadData: true,
  })

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = localization.slug

      return acc
    },
    { [locale]: slug },
  )
  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />
      {/* <ServerSiteHandler /> */}
      <PageBreadcrumb
        items={[
          { title: i18n._('Services'), href: '/services' },
          { title: pageData?.title },
        ]}
      />
      <PageContent pageData={pageData} />
    </>
  )
}
