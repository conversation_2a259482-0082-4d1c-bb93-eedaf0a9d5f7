import '@/styles/base.css'

import { APP_CONFIG } from '@/config-global'
import { cn } from '@ttplatform/ui/lib'
import type { Metadata, Viewport } from 'next'
import { Inter, Roboto } from 'next/font/google'
import { getLocale } from '../libs/data/cookies'
import { dynamicActivate } from '../localization/i18n-dynamic'
import LinguiClientProvider from '../providers/lingui-client-provider'
import NextAuthProvider from '../providers/next-auth-provider'

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
}

export const metadata: Metadata = {
  title: 'Kỷ niệm 15 năm Phú Thái Cat - Kiến tạo để Dẫn đầu',
  description:
    'Hành trình 15 năm đầy tự hào của Phú Thái Cat là minh chứng cho sự phát triển bền vững và không ngừng đổi mới. Từ những bước đi đầu tiên đến vị thế vững chắc hôm nay, chúng tôi luôn giữ vững cam kết hợp tác dài hạn và mang đến những giá trị tốt nhất cho khách hàng, đối tác và cộng đồng.',
  keywords:
    'Phú Thái Cat, 15 năm, Kiến tạo, Dẫn đầu, phát triển, hợp tác, cam kết, máy công trình, động cơ, máy phát điện, máy thủy, Caterpillar, bền vững',
  applicationName: 'Phú Thái Cat',
  metadataBase: new URL(APP_CONFIG.baseUrl),
  openGraph: {
    title: 'Phú Thái Cat',
    description:
      'Hành trình 15 năm đầy tự hào của Phú Thái Cat là minh chứng cho sự phát triển bền vững và không ngừng đổi mới. Từ những bước đi đầu tiên đến vị thế vững chắc hôm nay, chúng tôi luôn giữ vững cam kết hợp tác dài hạn và mang đến những giá trị tốt nhất cho khách hàng, đối tác và cộng đồng.',
    url: APP_CONFIG.baseUrl,
    siteName: 'PHÚ THÁI CAT',
    images: [
      {
        url: '/images/twitter-image.jpg',
        width: 1200,
        height: 630,
        alt: '',
      },
    ],
    locale: 'vi_VN',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: '',
    description: '',
    images: ['/images/twitter-image.jpg'],
  },
  icons: {
    icon: '/favicon.ico',
  },
  alternates: {
    canonical: APP_CONFIG.baseUrl,
    languages: {
      'en-US': `${APP_CONFIG.baseUrl}/en`,
      'vi-VN': `${APP_CONFIG.baseUrl}/vi`,
    },
  },
  // <meta property=“zalo-platform-site-verification” content=“GeM5A8k8FIX0WPi8XlytLM3AdccCl4WmDJWm” />
}

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const roboto = Roboto({
  subsets: ['latin'],
  variable: '--font-roboto',
  display: 'swap',
})

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const locale = await getLocale()

  const i18n = await dynamicActivate(locale)

  return (
    <html
      lang={locale}
      className={cn(inter.variable, roboto.variable, 'antialiased')}
    >
      <body suppressHydrationWarning={false} className="font-main">
        <LinguiClientProvider
          initialLocale={locale}
          initialMessages={i18n.messages}
        >
          <NextAuthProvider>{children}</NextAuthProvider>
        </LinguiClientProvider>
      </body>
    </html>
  )
}
