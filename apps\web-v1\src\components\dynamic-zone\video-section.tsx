'use client'

import GalleryVideos from '../media-gallery/gallery-videos'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
  video_gallery: any
}
//----------------------------------------------------------------------------------
export default function VideoSection({
  styles,
  heading,
  video_gallery,
}: TProps) {
  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <GalleryVideos
        gallery_videos={video_gallery}
        content_max_width={styles?.content_max_width}
      />
    </div>
  )

  if (!video_gallery && !video_gallery?.videos?.length) {
    return null
  }

  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
