import { cmsContentType } from '../../constants/cms'
import { QueryParams, useClientFetch } from './use-client-fetch'

const CNT_CAREERS = cmsContentType.careers
const CNT_CAREERS_CATEGORY = cmsContentType.careerCategories

export const useGetCareers = ({
  filters,
  locale,
  status,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_CAREERS,
    params: {
      filters,
      locale,
      status: status || 'published',
      sort: sort || 'publishedAt:desc',
      pagination,
      populate,
      fields,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}

/**
 * GET CAREER BY SLUG
 */
export const useGetCareerBySlug = ({
  slug,
  locale,
}: {
  slug: string
  locale?: string
}) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_CAREERS,
    params: {
      locale,
      filters: {
        slug,
      },
      populate: '*',
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}
/**
 * GET CAREER CATEGORIES
 */
export const useGetCareerCategories = ({
  filters,
  locale,
  status,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_CAREERS_CATEGORY,
    params: {
      filters,
      locale,
      status: status || 'published',
      sort: sort || 'publishedAt:desc',
      pagination,
      populate,
      fields,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}
