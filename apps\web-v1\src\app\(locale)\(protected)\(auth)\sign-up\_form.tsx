'use client'

import { paths } from '@/routes/paths'
import { t } from '@lingui/core/macro'
import { useForm } from '@tanstack/react-form'
import { Button, Checkbox, Input, Typography } from '@ttplatform/ui/components'
import { motion } from 'framer-motion'
import { Eye, EyeOff } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { toast } from 'sonner'
import { z } from 'zod'
import { AppLogo } from '~/src/components/logo'
import { register } from '~/src/libs/data/auth'

const formSchema = z
  .object({
    name: z.string().min(1, t`Name is required`),
    email: z.string().email(t`Invalid email format`),
    password: z.string().min(8, t`Password must be at least 8 characters`),
    confirm_password: z
      .string()
      .min(8, t`Confirm password must be at least 8 characters`),
    is_checked: z.boolean().refine((data) => data === true, {
      message: t`You must agree to the terms and conditions`,
      path: ['is_checked'],
    }),
  })
  .refine((data) => data.password === data.confirm_password, {
    message: t`Passwords must match`,
    path: ['confirm_password'],
  })
type FormValues = z.infer<typeof formSchema>
type FormFieldProps = {
  name: keyof FormValues
  label: string
  type?: 'text' | 'password' | 'email'
  placeholder: string
  showPassword?: boolean
  togglePassword?: () => void
  form: any
}

const FormField = ({
  name,
  label,
  type = 'text',
  placeholder,
  showPassword,
  togglePassword,
  form,
}: FormFieldProps) => {
  return (
    <form.Field name={name}>
      {({ state, handleChange, handleBlur }: any) => (
        <div className="mb-3">
          <Typography variant="body2" className="mb-1 font-medium">
            {label}
          </Typography>
          <div className="relative">
            <Input
              type={
                type === 'password'
                  ? showPassword
                    ? 'text'
                    : 'password'
                  : type
              }
              defaultValue={state.value as string}
              onChange={(e) => handleChange(e.target.value)}
              onBlur={handleBlur}
              placeholder={placeholder}
            />
            {type === 'password' && (
              <button
                type="button"
                className="absolute top-3 right-2 cursor-pointer text-gray-500"
                onClick={togglePassword}
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            )}
          </div>
          {state?.meta?.errors && state.meta.errors.length > 0 ? (
            <Typography variant="caption" className="mt-1 text-xs text-red-500">
              {state.meta.errors[0]?.message || String(state.meta.errors[0])}
            </Typography>
          ) : null}
        </div>
      )}
    </form.Field>
  )
}

export default function SignUpForm() {
  const router = useRouter()

  const [showPassword, setShowPassword] = useState({
    password: false,
    confirmPassword: false,
  })

  const defaultValues: FormValues = {
    name: '',
    email: '',
    password: '',
    confirm_password: '',
    is_checked: true,
  }

  const form = useForm({
    defaultValues,
    onSubmit: async ({ value }) => {
      try {
        const user = await register({
          name: value.name,
          email: value.email,
          password: value.password,
        })

        if (user) {
          toast.success(t`Account created successfully!`)
          router.push(paths.auth.login)
        }
      } catch (error) {
        console.error(error)
        toast.error(t`Sign-up failed`, {
          description:
            error instanceof Error ? error.message : t`Something went wrong`,
        })
      }
    },
    validators: {
      onChange: formSchema,
    },
  })

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: 'easeOut' }}
      className="relative mx-auto w-full max-w-xl p-4"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5, ease: 'easeOut' }}
        className="relative z-20 flex w-full flex-col items-center justify-center rounded-md p-8"
        style={{
          background: 'rgba(255, 255, 255, 1)',
          borderRadius: '16px',
          boxShadow: '0 4px 30px rgba(0, 0, 0, 0.03)',
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)',
        }}
      >
        <div className="flex gap-4 pb-6">
          <Typography variant="h3" className="my-3">
            {t`Welcome to`}{' '}
          </Typography>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <AppLogo />
          </motion.div>
        </div>

        <Typography variant="body2" className="max-w-xl text-center">
          {t`Join us today and unlock the full potential of`}{' '}
          <strong>PTC Platform</strong>.{' '}
          {t`Explore a range of features designed to elevate your business and drive growth.`}
        </Typography>

        <motion.form
          onSubmit={(e) => {
            e.preventDefault()
            form.handleSubmit()
          }}
          className="mt-4 w-full"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3, duration: 0.4 }}
          >
            <FormField
              name="name"
              label={t`Full Name`}
              placeholder={t`Enter your name`}
              form={form}
            />
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3, duration: 0.4 }}
          >
            <FormField
              name="email"
              label={t`Email`}
              placeholder={t`Enter your email`}
              form={form}
            />
          </motion.div>

          <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
            <FormField
              name="password"
              label={t`Password`}
              type="password"
              placeholder={t`Create password`}
              showPassword={showPassword.password}
              togglePassword={() =>
                setShowPassword({
                  ...showPassword,
                  password: !showPassword.password,
                })
              }
              form={form}
            />
            <FormField
              name="confirm_password"
              label={t`Confirm Password`}
              type="password"
              placeholder={t`Confirm password`}
              showPassword={showPassword.confirmPassword}
              togglePassword={() =>
                setShowPassword({
                  ...showPassword,
                  confirmPassword: !showPassword.confirmPassword,
                })
              }
              form={form}
            />
          </div>

          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.4 }}
          >
            <form.Field name="is_checked">
              {({ state, handleChange }) => (
                <div className="mb-3 flex flex-col">
                  <label className="inline-flex items-center text-xs">
                    <Checkbox
                      checked={state.value}
                      onCheckedChange={(checked) => handleChange(!!checked)}
                      className="h-4 w-4"
                    />
                    <Typography variant="body2" className="ml-2">
                      {t`I agree to the`}{' '}
                      <Link href="#" className="underline">
                        {t`Terms & Conditions`}
                      </Link>
                    </Typography>
                  </label>

                  {state.value.valueOf() === false && (
                    <Typography
                      variant="caption"
                      className="mt-1 text-xs text-red-500"
                    >
                      {t`You must agree to the terms and conditions`}
                    </Typography>
                  )}
                </div>
              )}
            </form.Field>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.5, duration: 0.3 }}
          >
            <Button
              type="submit"
              loading={form.state.isSubmitting}
              className="mt-3 h-10 w-full"
            >
              {t`Create Account`}
            </Button>
          </motion.div>
        </motion.form>
        <Typography variant="body2" className="mt-4 text-gray-600">
          {t`Already have an account?`}
          <Link
            href={paths.auth.login}
            className="ml-1 cursor-pointer underline"
          >
            {t`Sign in`}
          </Link>
        </Typography>
      </motion.div>
    </motion.div>
  )
}
