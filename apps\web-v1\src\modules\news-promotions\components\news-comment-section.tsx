'use client'
import {
  CommentBox,
  CommentInput,
  SectionTitle,
} from '@ttplatform/core-page-builder/components'
import { TMediaSchema } from '@ttplatform/core-page-builder/libs'
import { Button } from '@ttplatform/ui/components'
import { useState } from 'react'

export interface IComments {
  documentId?: string
  avatar?: TMediaSchema
  content?: string
  createdAt?: string
  name?: string
  is_guess?: boolean
  count_like?: number
  children?: IComments[]
  parent?: IComments | null
}

interface CommentSectionProps {
  initialComments?: IComments[]
  title?: string
  articleId?: string
}

const NewsCommentSection = ({
  initialComments,
  title = 'BÌNH LUẬN',
  // articleId,
}: CommentSectionProps) => {
  const [comments, setComments] = useState<IComments[]>(initialComments || [])
  // console.log('🚀 ~ NewsCommentSection ~ comments:', comments)
  const [visibleComments, setVisibleComments] = useState<number>(3)
  // const handleAddComment = async (text: string) => {
  //   const newComment: CommentData = {
  //     id: `comment-${Date.now()}`,
  //     author: {
  //       name: 'Bạn',
  //       avatarSrc: '/lovable-uploads/e9277b03-9b5b-4f6b-81fd-1d2628a3a950.png',
  //     },
  //     content: text,
  //     date: new Date().toISOString(),
  //     likes: 0,
  //   }
  //   setComments([newComment, ...comments])
  //   if (text.trim() === '') return
  //   const body = {
  //     data: {
  //       avatar: null,
  //       name: '',
  //       content: text,
  //       connect: [articleId],
  //     },
  //   }
  //   const response = await fetch(`${APP_CONFIG.apiUrl}/api${apiRoute.articleComments}`, {
  //     method: 'POST',
  //     body: JSON.stringify(body),
  //   })
  // }
  const handleAddReply = (parentId: string, text: string) => {
    const newReply: any = {
      id: `reply-${Date.now()}`,
      author: {
        name: 'Bạn',
        avatarSrc: '/lovable-uploads/e9277b03-9b5b-4f6b-81fd-1d2628a3a950.png',
      },
      content: text,
      date: new Date().toISOString(),
      likes: 0,
      parentId: parentId,
    }

    const addReplyToComment = (commentsList: any): any[] => {
      return commentsList.map((comment: any) => {
        if (comment.id === parentId) {
          return {
            ...comment,
            replies: comment.replies
              ? [...comment.replies, newReply]
              : [newReply],
          }
        } else if (comment.replies && comment.replies.length > 0) {
          return {
            ...comment,
            replies: addReplyToComment(comment.replies),
          }
        }
        return comment
      })
    }

    setComments(addReplyToComment(comments))
  }

  const loadMoreComments = () => {
    setVisibleComments((prevCount) => Math.min(prevCount + 3, comments.length))
  }
  return (
    <div className="w-full mx-auto rounded-lg p-10 bg-gray-50 shadow-sm">
      <SectionTitle text={title || ''} />

      <div className="my-10">
        <CommentInput onSubmit={() => {}} />
      </div>

      <div className="space-y-6">
        {comments.slice(0, visibleComments).map((comment, index) => (
          <CommentBox
            key={index}
            comment={comment}
            onAddReply={handleAddReply}
          />
        ))}
      </div>

      {visibleComments < comments.length && (
        <div className="flex justify-center mt-8">
          <Button
            onClick={loadMoreComments}
            variant="outline"
            className="border-yellow-400 text-black hover:bg-yellow-50"
          >
            TẢI THÊM BÌNH LUẬN
          </Button>
        </div>
      )}
    </div>
  )
}

export default NewsCommentSection
