'use client'

import { cn } from '@ttplatform/ui/lib'

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string
}

export function Skeleton({ className, ...props }: SkeletonProps) {
  return (
    <div
      className={cn(
        'animate-pulse rounded-md bg-gray-100 dark:bg-gray-400',
        className,
      )}
      {...props}
    />
  )
}

// Card Skeleton for news/promotions
export function CardSkeleton({ className }: { className?: string }) {
  return (
    <div
      className={cn('bg-white rounded-lg shadow-sm overflow-hidden', className)}
    >
      {/* Image skeleton */}
      <Skeleton className="h-48 w-full rounded-none" />

      {/* Content skeleton */}
      <div className="p-6 space-y-4">
        {/* Category badge */}
        <Skeleton className="h-5 w-20 rounded-full" />

        {/* Title */}
        <div className="space-y-2">
          <Skeleton className="h-6 w-full" />
          <Skeleton className="h-6 w-3/4" />
        </div>

        {/* Description */}
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-4 w-2/3" />
        </div>

        {/* Meta info */}
        <div className="flex items-center justify-between pt-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-16" />
        </div>
      </div>
    </div>
  )
}

// Testimonial Card Skeleton
export function TestimonialSkeleton({ className }: { className?: string }) {
  return (
    <div
      className={cn('bg-white rounded-lg shadow-sm p-6 space-y-4', className)}
    >
      {/* Stars skeleton */}
      <div className="flex space-x-1">
        {Array.from({ length: 5 }).map((_, i) => (
          <Skeleton key={i} className="h-4 w-4 rounded-sm" />
        ))}
      </div>

      {/* Quote content */}
      <div className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-5/6" />
        <Skeleton className="h-4 w-4/5" />
        <Skeleton className="h-4 w-3/4" />
      </div>

      {/* Author info */}
      <div className="flex items-center space-x-4 pt-4">
        {/* Avatar */}
        <Skeleton className="h-12 w-12 rounded-full flex-shrink-0" />

        {/* Name and position */}
        <div className="space-y-2 flex-1">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-4 w-24" />
        </div>
      </div>
    </div>
  )
}

// Grid Skeleton for multiple cards
export function GridSkeleton({
  items = 3,
  className,
}: {
  items?: number
  className?: string
}) {
  return (
    <div className={cn('grid gap-6 md:grid-cols-2 lg:grid-cols-3', className)}>
      {Array.from({ length: items }).map((_, index) => (
        <CardSkeleton key={index} />
      ))}
    </div>
  )
}

// Testimonials Grid Skeleton
export function TestimonialsGridSkeleton({
  items = 3,
  className,
}: {
  items?: number
  className?: string
}) {
  return (
    <div className={cn('grid gap-6 md:grid-cols-2 lg:grid-cols-3', className)}>
      {Array.from({ length: items }).map((_, index) => (
        <TestimonialSkeleton key={index} />
      ))}
    </div>
  )
}

// Section Skeleton with heading
export function SectionSkeleton({
  items = 3,
  showHeading = true,
  className,
}: {
  items?: number
  showHeading?: boolean
  className?: string
}) {
  return (
    <section className={cn('py-16 bg-gray-50', className)}>
      <div className="container mx-auto px-4">
        {showHeading && (
          <div className="text-center mb-12 space-y-4">
            {/* Heading skeleton */}
            <Skeleton className="h-10 w-80 mx-auto" />
            {/* Subheading skeleton */}
            <Skeleton className="h-6 w-96 mx-auto" />
          </div>
        )}

        {/* Cards grid skeleton */}
        <GridSkeleton items={items} />
      </div>
    </section>
  )
}

// Testimonials Section Skeleton
export function TestimonialsSectionSkeleton({
  items = 3,
  showHeading = true,
  className,
}: {
  items?: number
  showHeading?: boolean
  className?: string
}) {
  return (
    <section className={cn('py-16 bg-gray-50', className)}>
      <div className="container mx-auto px-4">
        {showHeading && (
          <div className="text-center mb-12 space-y-4">
            {/* Heading skeleton */}
            <Skeleton className="h-10 w-80 mx-auto" />
            {/* Subheading skeleton */}
            <Skeleton className="h-6 w-96 mx-auto" />
          </div>
        )}

        {/* Testimonials grid skeleton */}
        <TestimonialsGridSkeleton items={items} />
      </div>
    </section>
  )
}

// Image Slider Skeleton
export function ImageSliderSkeleton({
  showHeading = true,
  className,
}: {
  showHeading?: boolean
  className?: string
}) {
  return (
    <section className={cn('py-16 bg-gray-50', className)}>
      <div className="container mx-auto px-4">
        {showHeading && (
          <div className="text-center mb-12 space-y-4">
            {/* Heading skeleton */}
            <Skeleton className="h-10 w-80 mx-auto" />
            {/* Subheading skeleton */}
            <Skeleton className="h-6 w-96 mx-auto" />
          </div>
        )}

        {/* Slider container */}
        <div className="relative bg-white rounded-lg shadow-sm p-6">
          {/* Main slider image */}
          <Skeleton className="h-96 w-full rounded-lg" />

          {/* Navigation dots */}
          <div className="flex justify-center space-x-2 mt-6">
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className="h-3 w-3 rounded-full" />
            ))}
          </div>

          {/* Navigation arrows */}
          <div className="absolute inset-y-0 left-10 flex items-center">
            <Skeleton className="h-10 w-10 rounded-full" />
          </div>
          <div className="absolute inset-y-0 right-10 flex items-center">
            <Skeleton className="h-10 w-10 rounded-full" />
          </div>
        </div>

        {/* Thumbnail strip */}
        <div className="flex justify-center space-x-4 mt-8 overflow-hidden">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-sm p-2">
              <Skeleton className="h-16 w-24 rounded" />
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export const SkeletonVariants = {
  default: 'bg-gray-50',
  hero: 'bg-white',
  content: 'bg-gray-50',
  secondary: 'bg-gray-50',
} as const

// Enhanced Section Skeleton with variants
export function EnhancedSectionSkeleton({
  items = 3,
  showHeading = true,
  variant = 'default',
  className,
}: {
  items?: number
  showHeading?: boolean
  variant?: keyof typeof SkeletonVariants
  className?: string
}) {
  return (
    <section className={cn('py-16', SkeletonVariants[variant], className)}>
      <div className="container mx-auto px-4">
        {showHeading && (
          <div className="text-center mb-12 space-y-4">
            {/* Heading skeleton */}
            <Skeleton className="h-10 w-80 mx-auto" />
            {/* Subheading skeleton */}
            <Skeleton className="h-6 w-96 mx-auto" />
          </div>
        )}

        {/* Cards grid skeleton */}
        <GridSkeleton items={items} />
      </div>
    </section>
  )
}
