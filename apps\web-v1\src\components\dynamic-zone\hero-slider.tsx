'use client'

import 'swiper/css'
import 'swiper/css/navigation'

import {
  CmsMedia,
  HeadingSection,
} from '@ttplatform/core-page-builder/components'
import {
  TMediaSchema,
  getStrapiImageUrl,
} from '@ttplatform/core-page-builder/libs'
import { Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import Head from 'next/head'
import { useRef, useState } from 'react'
import { Autoplay, Navigation } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/react'
import { LINGUI_CONFIG } from '~/src/config-global'
//Component cho thumbnail slider
const ThumbnailSlide = ({
  media,
  index,
  activeIndex,
  onClick,
}: {
  media: any
  index: number
  activeIndex: number
  onClick: (index: number) => void
}) => {
  return (
    <div
      key={media.id}
      className={cn(
        'relative z-10 h-[60px] w-[72px] lg:h-[100px] lg:w-[120px] cursor-pointer overflow-hidden',
      )}
      style={{
        clipPath:
          'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
      }}
      onClick={() => onClick(index)}
    >
      <div
        className="absolute inset-0 z-20 h-[54px] w-[66px] lg:h-[94px] lg:w-[114px] -translate-x-1/2 -translate-y-1/2 left-1/2 top-1/2"
        style={{
          clipPath: 'inherit',
        }}
      >
        <CmsMedia
          media={media.thumbnail_image}
          sizes="(max-width: 768px) 100vw, 50vw"
          className="object-cover"
          priority
          fill
        />
      </div>

      <div
        className={cn(
          'absolute inset-0 z-10',
          activeIndex === index ? 'bg-yellow-400' : 'bg-white',
        )}
        style={{
          clipPath: 'inherit',
        }}
      />
    </div>
  )
}
export enum MediaExtensionType {
  JPG = 'jpg',
  PNG = 'png',
  WEBP = 'webp',
  MP4 = 'mp4',
  WEBM = 'webm',
}

export interface MediaItem {
  ext?: MediaExtensionType
  url?: string
  alternativeText?: string
}

interface THeroSectionFields {
  heading?: any
  media?: TMediaSchema | null
}

export const BHeroSection = ({
  items,
  locale,
}: {
  items: THeroSectionFields[]
  locale?: string
}) => {
  const swiperRef = useRef<any>(null)
  const prevRef = useRef<HTMLButtonElement>(null)
  const nextRef = useRef<HTMLButtonElement>(null)
  const [activeIndex, setActiveIndex] = useState(0)

  return (
    <section
      className={cn(
        'b-hero-section relative h-[100vh] overflow-hidden flex flex-col items-center justify-center',
      )}
    >
      <Head>
        <style>{`
          .hexagon {
            clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
          }
        `}</style>
      </Head>
      {items && items.length > 0 ? (
        <div className="absolute inset-0 z-0">
          <Swiper
            modules={[Navigation, Autoplay]}
            loop={true}
            slidesPerView={1}
            autoplay={{ delay: 4000, disableOnInteraction: true }}
            navigation={{
              prevEl: prevRef.current,
              nextEl: nextRef.current,
            }}
            onSwiper={(swiper) => {
              swiperRef.current = swiper
              setActiveIndex(swiper.realIndex)
            }}
            onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
            className="h-full w-full relative z-10"
          >
            {items.map((media, index) => {
              return (
                <SwiperSlide key={index} className="h-full w-full">
                  {media.media?.ext?.includes(MediaExtensionType.JPG) ||
                    media.media?.ext?.includes(MediaExtensionType.PNG) ||
                    (media.media?.ext?.includes(MediaExtensionType.WEBP) && (
                      <div className="relative h-full w-full">
                        <div className="absolute z-10 inset-0 bg-black/40" />
                        <CmsMedia
                          media={media.media}
                          className={cn(
                            'pointer-events-none h-full w-full object-cover',
                          )}
                          fill
                          sizes="(max-width: 768px) 100vw, 50vw"
                          priority
                        />
                      </div>
                    ))}
                  {media.media?.ext?.includes(MediaExtensionType.MP4) && (
                    <div className="relative h-full w-full">
                      <div className="absolute z-10 inset-0 bg-black/30" />
                      <video
                        src={getStrapiImageUrl(media.media)}
                        autoPlay
                        loop
                        muted
                        playsInline
                        className={cn(
                          'pointer-events-none h-full w-full object-cover',
                        )}
                      >
                        <source
                          src={getStrapiImageUrl(media.media)}
                          type="video/mp4"
                        />
                        Your browser does not support the video tag.
                      </video>
                    </div>
                  )}
                  <div className="absolute inset-0 z-10 flex items-center justify-center">
                    <div className="mx-auto w-full max-w-4xl px-4">
                      {media.heading && (
                        <div className="relative z-10 text-center">
                          <HeadingSection
                            hasUnderline={false}
                            heading={media.heading}
                            locale={locale}
                            buttonContainerClassName="items-center flex-col mt-6 gap-4"
                          ></HeadingSection>
                        </div>
                      )}
                    </div>
                  </div>
                </SwiperSlide>
              )
            })}
          </Swiper>

          <button
            ref={prevRef}
            className="hidden xs:block absolute top-1/2 left-4 z-40 -translate-y-1/2 p-2 text-white disabled:opacity-20"
          >
            <ChevronLeft className="h-10 w-10" />
          </button>
          <button
            ref={nextRef}
            className="hidden xs:block absolute top-1/2 right-4 z-40 -translate-y-1/2 p-2 text-white disabled:opacity-20"
          >
            <ChevronRight className="h-10 w-10" />
          </button>

          <div className="absolute bottom-4 sm:bottom-12 3xl:bottom-24 left-1/2 z-20 flex -translate-x-1/2 items-center justify-center gap-4">
            {items.map((media, index) => (
              <ThumbnailSlide
                key={index}
                media={media}
                index={index}
                activeIndex={activeIndex}
                onClick={(idx) => swiperRef.current?.slideToLoop(idx)}
              />
            ))}
          </div>
        </div>
      ) : (
        <div className="absolute inset-0 -z-10 flex items-center justify-center bg-gray-800">
          <Typography variant="body1" classNames={{ root: 'text-white' }}>
            No active media items
          </Typography>
        </div>
      )}
    </section>
    // <></>
  )
}

const HeroSlider = ({
  items,
  locale = LINGUI_CONFIG.defaultLocale,
}: {
  items: any[]
  locale?: string
}) => {
  return <BHeroSection items={items} locale={locale} />
}

export default HeroSlider
