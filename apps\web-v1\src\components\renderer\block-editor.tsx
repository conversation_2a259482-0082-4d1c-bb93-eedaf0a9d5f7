'use client'
import Link from 'next/link'

import { Icon } from '@iconify/react'
import {
  type BlocksContent,
  BlocksRenderer,
} from '@strapi/blocks-react-renderer'
import { Typography } from '@ttplatform/ui/components'
import { cn } from '~/src/utils'

export default function BlockEditor({
  content,
}: { readonly content: BlocksContent }) {
  if (!content) return null
  return (
    <div className="flex flex-col gap-4">
      <BlocksRenderer
        content={content}
        blocks={{
          // You can use the default components to set class names...
          paragraph: ({ children }) => (
            // <p className="text-gay-800 w-full">{children}</p>
            <Typography variant="body2" className="text-gay-800 w-full">
              {children}
            </Typography>
          ),
          // ...or point to a design system
          heading: ({ children, level }) => {
            switch (level) {
              case 1:
                return <Typography variant="h1">{children}</Typography>
              case 2:
                return <Typography variant="h2">{children}</Typography>
              case 3:
                return <Typography variant="h3">{children}</Typography>
              case 4:
                return <Typography variant="h4">{children}</Typography>
              case 5:
                return <Typography variant="h5">{children}</Typography>
              case 6:
                return <Typography variant="h6">{children}</Typography>
              default:
                return <Typography variant="h1">{children}</Typography>
            }
          },
          // For links, you may want to use the component from your router or framework
          link: ({ children, url }) => <Link href={url}>{children}</Link>,
          list: ({ children, format }) => (
            <ul
              className={cn(
                'list-inside',
                format === 'ordered' ? 'list-decimal' : '',
                format === 'unordered' ? 'list-disc' : '',
              )}
            >
              {children}
            </ul>
          ),
          'list-item': ({ children }) => (
            <li className="flex gap-2 items-start space-y-4">
              <Icon
                icon="mdi:hexagon"
                className="mt-0.5 shrink-0 text-primary"
                width={12}
                height={12}
              />
              <Typography variant="body2" className="text-black">
                {children}
              </Typography>
            </li>
          ),
        }}
        modifiers={{
          bold: ({ children }) => <strong>{children}</strong>,
          italic: ({ children }) => <span className="italic">{children}</span>,
        }}
      />
    </div>
  )
}
