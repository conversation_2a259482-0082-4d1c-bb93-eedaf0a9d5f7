import { SafeHTML, Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { motion } from 'framer-motion'
import React from 'react'
import { HexagonIcon } from '../../../atoms'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '../../../molecules'

interface FAQItem {
  id: string
  question: string
  answer: string[]
}

interface SupportFAQTabProps {
  faqItems: FAQItem[]
  accordionVariants: any
  isInView: boolean
}

export const SupportFAQTab: React.FC<SupportFAQTabProps> = ({
  faqItems,
  accordionVariants,
  isInView,
}) => (
  <Accordion
    type="single"
    collapsible
    defaultValue="faq-1"
    className="flex flex-col gap-4"
  >
    {faqItems.map((faq, index) => (
      <motion.div
        key={faq.id}
        variants={accordionVariants as any}
        initial="hidden"
        animate={isInView ? 'visible' : 'hidden'}
        custom={index}
      >
        <AccordionItem
          value={faq.id}
          className="rounded-lg border-none bg-white last:mb-0 shadow-[0px_1px_2px_0px_rgba(16,24,40,0.06),0px_1px_3px_0px_rgba(16,24,40,0.1)]"
        >
          <AccordionTrigger className="px-5 pl-6 py-5 data-[state=open]:pb-3 hover:no-underline cursor-pointer text-black">
            <Typography variant="body2" className="font-semibold">
              {faq.question}
            </Typography>
          </AccordionTrigger>
          <AccordionContent className={cn('px-5 pl-6 pb-4 text-gray-600')}>
            <ul className="space-y-2">
              {faq.answer.map((item, index) => (
                <li key={index} className="flex gap-2 items-baseline">
                  <HexagonIcon size={12} className="shrink-0" />
                  <Typography variant="body2" className="font-normal">
                    <SafeHTML html={item} />
                  </Typography>
                </li>
              ))}
            </ul>
          </AccordionContent>
        </AccordionItem>
      </motion.div>
    ))}
  </Accordion>
)
