import { cn } from '~/src/utils'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'
//-------------------------------------------------------------------
type CustomSectionRenderProps = {
  styles: any
  children: React.ReactNode
}
//-------------------------------------------------------------------
export default function CustomSectionRender({
  styles,
  children,
}: CustomSectionRenderProps) {
  const { background, content_max_width } = styles
  const renderMaxWidth = (maxWidth: string) => {
    switch (maxWidth) {
      case 'full_width':
        return ''
      case 'xl':
        return 'max-w-7xl'
      case 'lg':
        return 'max-w-6xl'
      case 'md':
        return 'max-w-5xl'
      case 'sm':
        return 'max-w-4xl'
      case 'xs':
        return 'max-w-3xl'
      default:
        return 'max-w-7xl'
    }
  }
  return (
    <section
      style={{
        backgroundImage: background?.image?.[0]?.url
          ? `url(${RenderImageUrlStrapi({ url: background?.image?.[0]?.url })})`
          : 'unset',
        backgroundColor: background?.color,
        backgroundPosition: 'center center',
        backgroundRepeat: background?.repeat,
        backgroundSize: background?.size,
        position: 'relative',
        opacity: background?.opacity,
      }}
    >
      <div
        className={cn(
          'container mx-auto py-10 md:py-20 px-4 min-[1536px]:max-w-7xl 2xl:max-w-[1600px]',
          renderMaxWidth(content_max_width),
        )}
      >
        {children}
      </div>
    </section>
  )
}
