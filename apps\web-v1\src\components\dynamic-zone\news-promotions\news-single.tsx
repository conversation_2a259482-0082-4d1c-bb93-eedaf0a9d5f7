import {
  TArticleSchema,
  THeadingSchema,
  TSectionStyleSchema,
} from '@ttplatform/core-page-builder/libs'

type TProps = {
  heading: THeadingSchema
  styles: TSectionStyleSchema
  locale: string
  item: TArticleSchema
}

const NewsSingle = ({ heading, styles, locale, item }: TProps) => {
  return (
    <div>
      <div>Heading: {JSON.stringify(heading)}</div>
      <div>Styles: {JSON.stringify(styles)}</div>
      <div>Locale: {locale}</div>
      <div>Item: {JSON.stringify(item)}</div>
    </div>
  )
}

export default NewsSingle
