import { Typography } from '@ttplatform/ui/components'
import { cn } from '~/src/utils'
import ButtonsCms from './buttons-cms'

export default function HeadingSection({
  heading,
  buttonsClassName,
  isDisabledIcon = true,
  underlineBackground,
}: {
  heading: any
  buttonsClassName?: string
  isDisabledIcon?: boolean
  underlineBackground?: string
}) {
  const renderHeadingAlign = (text_align: string) => {
    switch (text_align) {
      case 'text-center':
        return 'center'
      case 'text-right':
        return 'end'
      default:
        return 'start'
    }
  }
  const renderSubHeadingAlign = (text_align: string) => {
    switch (text_align) {
      case 'text-center':
        return 'center'
      case 'text-right':
        return 'right'
      case 'text-justify':
        return 'justify'
      default:
        return 'left'
    }
  }

  const renderHeading = (
    <div
      className="w-full flex flex-col"
      style={{
        maxWidth: heading?.type == 'grid' ? 700 : 'unset',
        alignItems:
          renderHeadingAlign(heading?.heading?.styles?.text_align) || 'start',
      }}
    >
      <Typography
        variant="h2"
        className={cn('uppercase')}
        style={{
          color: heading?.heading?.styles?.color || '#182230',
        }}
      >
        {heading?.heading?.text}
      </Typography>
      <div
        className="block mt-1 w-[55px] md:w-[68px] h-[5px] md:h-[8px] rounded-none"
        style={{
          background:
            underlineBackground ||
            'linear-gradient(to right, #FFCC00, #FFD326, #FFDE59)',
        }}
      ></div>
    </div>
  )
  const renderSubHeading = (
    <div>
      <Typography
        variant="body1"
        className={cn('font-normal')}
        style={{
          color: heading?.sub_heading?.styles?.color || '#344054',
          textAlign:
            renderSubHeadingAlign(heading?.sub_heading?.styles?.text_align) ||
            'left',
        }}
      >
        {heading?.sub_heading?.text}
      </Typography>
    </div>
  )
  const renderButtons = (
    <ButtonsCms
      buttons={heading?.buttons}
      className={buttonsClassName}
      isDisabledIcon={isDisabledIcon}
    />
  )
  return (
    <div className="w-full">
      {heading?.type == 'grid' ? (
        <div className="flex flex-col md:flex-row gap-y-4 gap-x-5 md:gap-x-10 lg:gap-x-20 justify-between">
          {heading?.heading?.text ? renderHeading : null}
          <div
            className={cn(
              'flex flex-col gap-y-4 w-full md:max-w-[500px]',
              heading?.sub_heading?.text
                ? 'items-start'
                : 'items-start md:items-end',
            )}
          >
            {heading?.sub_heading?.text ? renderSubHeading : null}
            {heading?.buttons?.length > 0 ? renderButtons : null}
          </div>
        </div>
      ) : null}
      {heading?.type == 'default' ? (
        <div className="flex flex-col gap-y-4">
          {heading?.heading?.text ? renderHeading : null}
          {heading?.sub_heading?.text ? renderSubHeading : null}
          {heading?.buttons?.length > 0 ? renderButtons : null}
        </div>
      ) : null}
    </div>
  )
}
