// import { useParams } from 'next/navigation';
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
}
//----------------------------------------------------------------------------------
export default function RecentlyViewedProducts({ styles, heading }: TProps) {
  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
