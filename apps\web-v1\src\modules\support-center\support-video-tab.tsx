'use client'

import { APP_CONFIG } from '@/config-global'
import { PlayVideoIcon } from '@ttplatform/core-page-builder/components'
import { TSupportCenterSchema } from '@ttplatform/core-page-builder/libs'
import { Typography } from '@ttplatform/ui/components'
import Image from 'next/image'
import { useState } from 'react'

interface SupportVideoTabProps {
  items: TSupportCenterSchema[]
  isInView: boolean
  accordionVariants: any
  searchQuery?: string
}

export const SupportVideoTab = ({
  items,
  searchQuery: parentSearchQuery = '',
}: SupportVideoTabProps) => {
  // Search is now handled by parent SearchBar component
  const [openVideo, setOpenVideo] = useState<any | TSupportCenterSchema>(null)

  const DEFAULT_API_URL = APP_CONFIG.apiUrl
  function getMediaUrl(url?: string | null) {
    if (!url) return '/images/products/may-cong-trinh-sem.png'
    if (url.startsWith('http')) return url
    return DEFAULT_API_URL.replace(/\/$/, '') + url
  }

  return (
    <>
      <div className="grid grid-cols-1 lg:grid-cols-2 3xl:grid-cols-3 gap-4">
        {items?.length > 0 ? (
          items.map((video) => (
            <div
              key={video.id}
              className="rounded-lg overflow-hidden shadow bg-white flex flex-col cursor-pointer"
              onClick={() => setOpenVideo(video)}
            >
              <div className="relative aspect-video w-full">
                <Image
                  src={getMediaUrl((video as any)?.thumbnail?.url)}
                  alt="video thumbnail"
                  width={400}
                  height={225}
                  className="object-cover w-full h-full"
                  priority
                />
                <button className="absolute inset-0 flex items-center justify-center text-white text-6xl pointer-events-none">
                  <PlayVideoIcon />
                </button>
              </div>
              <div
                className="w-full h-1"
                style={{
                  backgroundImage:
                    'linear-gradient(270deg, rgb(255, 222, 89) 0%, rgb(255, 211, 38) 16%, rgb(255, 204, 0) 42%)',
                }}
              />
              <Typography className="p-4 3xl:p-6 font-semibold" variant="body1">
                {video.name}
              </Typography>
            </div>
          ))
        ) : parentSearchQuery.trim() ? (
          <div className="col-span-full text-center py-12">
            <div className="flex flex-col items-center gap-4">
              <div className="text-6xl">🔍</div>
              <Typography variant="h6" className="text-gray-600 font-semibold">
                Không tìm thấy video nào
              </Typography>
              <Typography variant="body2" className="text-gray-500">
                Không có video nào phù hợp với từ khóa "{parentSearchQuery}"
              </Typography>
              <Typography variant="body2" className="text-gray-400">
                Hãy thử tìm kiếm với từ khóa khác
              </Typography>
            </div>
          </div>
        ) : (
          <div className="col-span-full text-center py-8">
            <Typography variant="body2" className="text-gray-500">
              Chưa có video nào trong chủ đề này
            </Typography>
          </div>
        )}
      </div>
      {/* Video Modal */}
      {openVideo && (
        <div
          className="fixed inset-0 z-[99999] flex items-center justify-center bg-black/80"
          onClick={() => setOpenVideo(null)}
        >
          <button
            className="absolute top-2 right-2 text-white text-2xl cursor-pointer"
            onClick={() => setOpenVideo(null)}
          >
            ×
          </button>
          <div
            className="bg-gray-50 shadow-lg max-w-4xl w-full relative"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="aspect-video w-full mb-4">
              {openVideo.video_embed ? (
                <div className="w-full h-full aspect-video">
                  <div
                    className="w-full h-full"
                    dangerouslySetInnerHTML={{
                      __html: openVideo.video_embed.replace(
                        /<iframe([^>]*)>/,
                        (_match: string, attrs: string) =>
                          `<iframe${attrs} style="width:100%;height:100%;" width="100%" height="100%">`,
                      ),
                    }}
                  />
                </div>
              ) : openVideo.video?.url ? (
                <video
                  src={getMediaUrl(openVideo.video.url)}
                  controls
                  autoPlay
                  className="object-cover w-full h-full bg-black"
                  poster={getMediaUrl(openVideo.video?.thumbnail?.url)}
                >
                  <track
                    kind="captions"
                    src=""
                    srcLang="vi"
                    label="Vietnamese"
                    default
                  />
                </video>
              ) : (
                <Image
                  src={getMediaUrl(openVideo.video?.thumbnail?.url)}
                  alt="video thumbnail"
                  width={400}
                  height={225}
                  className="object-cover w-full h-full"
                  onError={(e) => {
                    const target = e.currentTarget as HTMLImageElement
                    if (target.src !== '/images/fallback.png')
                      target.src = '/images/fallback.png'
                  }}
                  priority
                />
              )}
            </div>
            <div className="px-4 py-2">
              <Typography
                className="text-base font-semibold mb-2"
                variant="body1"
              >
                {openVideo.name}
              </Typography>
              {openVideo.description && (
                <Typography className="text-sm text-gray-700" variant="body2">
                  {openVideo.description}
                </Typography>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  )
}
