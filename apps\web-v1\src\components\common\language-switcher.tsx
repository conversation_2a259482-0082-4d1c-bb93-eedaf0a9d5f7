'use client'

import { Icon } from '@iconify/react'
import { cn } from '@ttplatform/ui/lib'
import { usePathname, useRouter } from 'next/navigation'
import { useSlugContext } from '~/src/context/slug-context'

import {
  Button,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@ttplatform/ui/components'
import { useState } from 'react'
import { LINGUI_CONFIG } from '~/src/config-global'
import { setLocaleClient } from '~/src/libs/data/cookies-client'
import { useBoolean } from '~/src/libs/hooks/use-boolean'

const staticPages = [
  'home',
  'product-categories',
  'product-category-single',
  'product-single',
  'service-categories',
  'service-category-single',
  'service-single',
  'blog-page',
  'blog-single',
  // 'about-us',
  // 'contact-us',
  'privacy-policy',
  'terms-of-service',
  'faq',
  'career-categories',
  'career-category-single',
  'career-single',
  'industrial-solutions',
  'industrial-solution-category-single',
  'industrial-solution-single',
  'application-areas',
  'branches',
  'brands',
  // 'faqs',
  'careers',
  'services',
  'products',
  'promotions',
  'promotion-categories',
  'article-categories',
  'article-single',
]

const LanguageSwitcher = ({ currentLocale }: { currentLocale: string }) => {
  const [curLocale, setCurLocale] = useState(currentLocale)
  const router = useRouter()
  const pathname = usePathname()

  const { state } = useSlugContext()
  const { localizedSlugs } = state

  const open = useBoolean(false)
  const loading = useBoolean(false)

  const switchLocale = (locale: string) => {
    loading.onTrue()
    try {
      setCurLocale(locale)
      setLocaleClient(locale)

      const isHome = pathname === '/'
      const isStaticPage = staticPages.includes(pathname.split('/').pop() || '')

      if (localizedSlugs[locale] && !isHome && !isStaticPage) {
        const newSlug = localizedSlugs[locale]
        router.replace(`/${newSlug}`)
      }

      router.refresh()
    } catch (error) {
      console.error(error)
    } finally {
      open.onFalse()
      loading.onFalse()
    }
  }

  // if (loading.value) {
  //   return (
  //     <div className="fixed inset-0 z-50 bg-white/50 backdrop-blur-sm flex justify-center items-center">
  //       <div className="w-20 h-20 rounded-full animate-spin"></div>
  //     </div>
  //   )
  // }

  return (
    <div className="flex gap-2 p-1 rounded-md z-[100]">
      <Popover
        defaultOpen={false}
        open={open.value}
        onOpenChange={open.onToggle}
      >
        <PopoverTrigger asChild>
          <Button variant="text">
            <Icon
              icon={`twemoji:flag-${curLocale.includes('vi') ? 'vietnam' : 'united-kingdom'}`}
              className="w- h-"
            />
            <span className="text-sm uppercase">{curLocale}</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-30 z-[100] p-0  rounded-sm overflow-hidden">
          {LINGUI_CONFIG.locales.map((item, idx) => (
            <Button
              key={idx}
              onClick={() => {
                switchLocale(item)
              }}
              variant="text"
              className={cn(
                'w-full hover:text-red-800 transition-all duration-300 rounded-none',
                item === curLocale ? 'font-bold bg-primary' : '',
              )}
            >
              <Icon
                icon={`twemoji:flag-${item.includes('vi') ? 'vietnam' : 'united-kingdom'}`}
                className="w-8 h-8"
              />
              <span className="text-sm uppercase w-8">{item}</span>
            </Button>
          ))}
        </PopoverContent>
      </Popover>
    </div>
  )
}

export default LanguageSwitcher
