{"kind": "collectionType", "collectionName": "articles", "info": {"singularName": "article", "pluralName": "articles", "displayName": "Articles", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "slug": {"type": "uid", "pluginOptions": {"i18n": {"localized": true}}, "targetField": "title"}, "image": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "category": {"type": "relation", "relation": "manyToOne", "target": "api::article-category.article-category", "inversedBy": "articles"}, "dynamic_zone": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["elementals.media-video", "elementals.media-image", "dynamic-zone.related-products", "elementals.compare-images", "elementals.editor", "dynamic-zone.latest-promotions-section", "dynamic-zone.latest-news-section"]}, "seo": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "shared.seo", "repeatable": false}, "featured": {"type": "boolean", "pluginOptions": {"i18n": {"localized": true}}}, "article_comments": {"type": "relation", "relation": "oneToMany", "target": "api::article-comment.article-comment", "mappedBy": "article"}, "view": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}, "min": 0}}}