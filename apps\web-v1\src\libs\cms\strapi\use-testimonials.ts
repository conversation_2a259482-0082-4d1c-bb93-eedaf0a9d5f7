import { cmsContentType } from '../../constants/cms'
import { QueryParams, useClientFetch } from './use-client-fetch'

const CNT_TESTIMONIALS = cmsContentType.testimonials

export const useGetTestimonials = ({
  filters,
  locale,
  status,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_TESTIMONIALS,
    params: {
      filters,
      locale,
      status: status || 'published',
      sort: sort || 'publishedAt:desc',
      pagination,
      populate,
      fields,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}

/**
 * GET TESTIMONIAL BY SLUG
 */
export const useGetTestimonialBySlug = ({
  slug,
  locale,
}: {
  slug: string
  locale?: string
}) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_TESTIMONIALS,
    params: {
      locale,
      filters: {
        slug,
      },
      populate: '*',
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}
