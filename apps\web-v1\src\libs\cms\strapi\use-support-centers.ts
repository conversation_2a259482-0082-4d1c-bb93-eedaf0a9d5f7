import { cmsContentType } from '../../constants/cms'
import { QueryParams, useClientFetch } from './use-client-fetch'

const CNT_SUPPORT_CENTERS = cmsContentType.supportCenters
const CNT_SUPPORT_CENTER_CATEGORIES = cmsContentType.supportCenterCategories

// #########################################################################################
// SUPPORT CENTER ITEMS
// #########################################################################################

/**
 * GET LIST SUPPORT CENTERS
 */
export const useGetSupportCenters = ({
  filters,
  locale,
  status,
  sort,
  pagination,
  populate,
  fields,
  search,
}: QueryParams & { search?: string }) => {
  const searchFilters = search?.trim()
    ? {
        $or: [
          { name: { $containsi: search.trim() } },
          { description: { $containsi: search.trim() } },
          { content: { $containsi: search.trim() } },
        ],
      }
    : {}

  const combinedFilters = search?.trim()
    ? {
        ...filters,
        ...searchFilters,
      }
    : filters

  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_SUPPORT_CENTERS,
    params: {
      filters: combinedFilters,
      locale,
      status: status || 'published',
      sort: sort || 'rank:asc,publishedAt:desc',
      pagination,
      populate,
      fields,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}

/**
 * GET SUPPORT CENTER BY SLUG
 */
export const useGetSupportCenterBySlug = ({
  slug,
  locale,
}: {
  slug: string
  locale?: string
}) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_SUPPORT_CENTERS,
    params: {
      locale,
      filters: {
        slug,
      },
      populate: '*',
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}

/**
 * GET SUPPORT CENTER LIST BY CATEGORY ID
 */
export const useGetSupportCentersByCategoryId = ({
  categoryId,
  locale,
}: {
  categoryId: string
  locale?: string
}) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_SUPPORT_CENTERS,
    params: {
      filters: {
        category: categoryId,
      },
      locale,
      populate: '*',
    },
  })
  return {
    data,
    isLoading,
    error,
    mutate,
  }
}

// #########################################################################################
// SUPPORT CENTER CATEGORY
// #########################################################################################

/**
 * GET LIST SUPPORT CENTER CATEGORIES
 */
export const useGetSupportCenterCategories = ({
  filters,
  locale,
  status,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_SUPPORT_CENTER_CATEGORIES,
    params: {
      filters,
      locale,
      status,
      sort,
      pagination,
      populate,
      fields,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}

/**
 * GET SUPPORT CENTER CATEGORY BY ID
 */
export const useGetSupportCenterCategoryById = ({
  id,
  locale,
}: {
  id: string
  locale?: string
}) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_SUPPORT_CENTER_CATEGORIES,
    params: {
      filters: {
        id,
      },
      locale,
      populate: '*',
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}
