'use client'

import { TPromotionSchema } from '@ttplatform/core-page-builder/libs'
import { useGetPromotions } from '~/src/libs/cms/strapi/use-promotions'
import { useLocale } from '~/src/libs/data/use-locale'
import { FeaturedNewsPromotions } from '~/src/modules/news-promotions'
import { EmptyContent } from '../../../../../../packages/ui/src/templates/empty'

type TProps = {
  limit: number
  custom_promotions: TPromotionSchema[]
}

const PromotionFeatured = ({ limit, custom_promotions }: TProps) => {
  const locale = useLocale()
  if (custom_promotions?.length > 0) {
    return (
      <FeaturedNewsPromotions items={custom_promotions} type="promotions" />
    )
  }

  const { data, isLoading, error } = useGetPromotions({
    locale,
    filters: {
      featured: true,
    },
    pagination: {
      limit,
    },
  })

  if (isLoading) {
    return (
      <div className="w-12 h-12 border-4 border-gray-50 border-t-transparent rounded-full animate-spin"></div>
    )
  }

  const notFound = !isLoading && error && !data?.data.length

  if (notFound) {
    return <EmptyContent title="Đang cập nhật..." />
  }

  return <FeaturedNewsPromotions items={data?.data} type="promotions" />
}

export default PromotionFeatured
