{"collectionName": "components_dynamic_zone_testimonials_sections", "info": {"displayName": "Testimonials_Section"}, "options": {}, "attributes": {"adminLabel": {"type": "string", "private": true}, "styles": {"type": "component", "component": "styles.section-styles", "repeatable": false}, "items": {"type": "relation", "relation": "oneToMany", "target": "api::testimonial.testimonial"}, "heading": {"type": "component", "component": "elementals.heading", "repeatable": false}}, "config": {}}