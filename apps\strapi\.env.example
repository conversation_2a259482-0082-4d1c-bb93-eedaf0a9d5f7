HOST=0.0.0.0
PORT=1337
APP_KEYS="toBeModified1,toBeModified2"
API_TOKEN_SALT=tobemodified
ADMIN_JWT_SECRET=tobemodified
TRANSFER_TOKEN_SALT=tobemodified
JWT_SECRET=tobemodified

CLIENT_URL=http://localhost:3000
PREVIEW_SECRET=tobemodified # optional, required with Next.js draft mode

# Postgresql
DATABASE_CLIENT=mysql|postgres|sqlite
DATABASE_PORT=tobemodified
DATABASE_NAME=tobemodified
DATABASE_USERNAME=tobemodified
DATABASE_PASSWORD=tobemodified
DATABASE_SSL=tobemodified
DATABASE_SSL_KEY=tobemodified
DATABASE_SSL_CERT=tobemodified
DATABASE_SSL_CA=tobemodified
DATABASE_SSL_CAPATH=tobemodified
DATABASE_SSL_CIPHER=tobemodified
DATABASE_SSL_REJECT_UNAUTHORIZED=tobemodified


REDIS_URL=
