'use client'

import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { APP_CONFIG } from '~/src/config-global'
import { useSlugContext } from '~/src/context/slug-context'
import { useLocale } from '~/src/libs/data/use-locale'
import { dynamicActivate } from '~/src/localization/i18n-dynamic'

export default function ClientSlugHandler({
  localizedSlugs,
}: {
  localizedSlugs: Record<string, string>
}) {
  const { dispatch } = useSlugContext()
  const locale = useLocale()
  const router = useRouter()

  useEffect(() => {
    if (localizedSlugs) {
      dispatch({ type: 'SET_SLUGS', payload: localizedSlugs })
    }
  }, [localizedSlugs, dispatch])

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    const handleMessage = async (message: MessageEvent<any>) => {
      // process.env.NEXT_PUBLIC_API_URL
      if (
        message.origin === APP_CONFIG.apiUrl &&
        message.data.type === 'strapiUpdate'
      ) {
        router.refresh()
      }
    }

    // Add the event listener
    window.addEventListener('message', handleMessage)

    // Cleanup the event listener on unmount
    return () => {
      window.removeEventListener('message', handleMessage)
    }
  }, [router, locale])

  useEffect(() => {
    const fetchI18n = async () => {
      await dynamicActivate(locale)
    }
    fetchI18n()
  }, [locale])

  return null // This component only handles the state and doesn't render anything.
}
