import { MainButton } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
//---------------------------------------------------------------------------------
type TProps = {
  documents: any
}
//---------------------------------------------------------------------------------
export default function GalleryDocuments({ documents }: TProps) {
  return (
    <div className="w-full max-w-screen-lg mx-auto h-auto flex flex-col divide-y divide-white/20">
      {documents?.map((item: any, idx: number) => (
        <div key={idx} className="flex gap-5 justify-between py-10">
          <Typography variant={'body1'} className="text-white">
            {item?.name}
          </Typography>
          <MainButton variant={'primary'} />
        </div>
      ))}
    </div>
  )
}
