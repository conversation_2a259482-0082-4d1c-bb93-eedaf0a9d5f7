import React from 'react'
import { cn } from '~/src/utils'

type ButtonVariant = 'simple' | 'outline-solid' | 'primary' | 'muted'

const variantClasses: Record<ButtonVariant, string> = {
  simple:
    'bg-secondary bg-transparent hover:border-secondary/50 hover:bg-secondary/10 border border-transparent text-white',
  'outline-solid':
    'bg-white hover:bg-secondary/90 hover:shadow-xl text-black border border-black',
  primary:
    'bg-secondary hover:bg-secondary/90 border border-secondary text-black shadow-[0px_-1px_0px_0px_#FFFFFF60_inset,_0px_1px_0px_0px_#FFFFFF60_inset] hover:-translate-y-1 active:-translate-y-0',
  muted:
    'bg-neutral-800 hover:bg-neutral-900 border border-transparent text-white shadow-[0px_1px_0px_0px_#FFFFFF20_inset]',
}

type ButtonProps<T extends React.ElementType> = {
  as?: T
  variant?: ButtonVariant
  className?: string
  children?: React.ReactNode
} & Omit<
  React.ComponentPropsWithoutRef<T>,
  'as' | 'variant' | 'className' | 'children'
>

export const Button = <T extends React.ElementType = 'button'>({
  as,
  variant = 'primary',
  className,
  children,
  ...props
}: ButtonProps<T>) => {
  const Component = as || 'button'
  return (
    <Component
      className={cn(
        'relative z-10 transition font-medium duration-200 rounded-md px-4 py-2 flex items-center justify-center text-sm md:text-sm',
        variantClasses[variant as ButtonVariant],
        className,
      )}
      {...(props as any)}
    >
      {children ?? 'Get Started'}
    </Component>
  )
}
