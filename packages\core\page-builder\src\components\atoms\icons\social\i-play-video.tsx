// components/icons/PlayIcon.tsx

import React from 'react'

interface PlayIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number
  color?: string
  className?: string
}

export const PlayVideoIcon: React.FC<PlayIconProps> = ({
  size = 64,
  className,
}) => {
  const computedHeight = (size / 64) * 46
  return (
    <svg
      width={size}
      height={computedHeight}
      viewBox="0 0 64 46"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M62.7253 7.49312C62.3635 6.13156 61.6504 4.88891 60.6574 3.88954C59.6644 2.89017 58.4263 2.16913 57.0671 1.79858C52.0635 0.453125 32.0635 0.453125 32.0635 0.453125C32.0635 0.453125 12.0635 0.453125 7.05984 1.79858C5.70062 2.16913 4.46257 2.89017 3.46958 3.88954C2.47659 4.88891 1.76349 6.13156 1.40166 7.49312C0.0634767 12.5186 0.0634766 22.9986 0.0634766 22.9986C0.0634766 22.9986 0.0634767 33.4786 1.40166 38.504C1.76349 39.8656 2.47659 41.1082 3.46958 42.1076C4.46257 43.107 5.70062 43.828 7.05984 44.1986C12.0635 45.544 32.0635 45.544 32.0635 45.544C32.0635 45.544 52.0635 45.544 57.0671 44.1986C58.4263 43.828 59.6644 43.107 60.6574 42.1076C61.6504 41.1082 62.3635 39.8656 62.7253 38.504C64.0635 33.4786 64.0635 22.9986 64.0635 22.9986C64.0635 22.9986 64.0635 12.5186 62.7253 7.49312Z"
        fill="#121212"
        fillOpacity="0.72"
      />
      <path
        d="M25.5181 32.5171V13.4844L42.2453 23.0007L25.5181 32.5171Z"
        fill="#FEFEFE"
      />
    </svg>
  )
}
