'use client'

import {
  Container<PERSON><PERSON><PERSON>,
  HexIconWrapper,
} from '@ttplatform/core-page-builder/components'
import {
  THeadingSchema,
  TPaginationSchema,
  TSectionStyleSchema,
  TSupportCenterCategorySchema,
  TSupportCenterSchema,
} from '@ttplatform/core-page-builder/libs'
import {
  Button,
  Card,
  CardContent,
  Input,
  Skeleton,
  Typography,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { useInView } from 'framer-motion'
import { ArrowRight, SearchIcon } from 'lucide-react'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { useGetSupportCenters } from '~/src/libs/cms/strapi/use-support-centers'
import { useLocale } from '~/src/libs/data/use-locale'
import { SupportFAQTab } from './support-faq-tab'
import { SupportTechnicalTab } from './support-technical-tab'
import { SupportVideoTab } from './support-video-tab'

// interface FAQItem {
//   id: string
//   question: string
//   answer: string[]
// }

// interface TopicItem {
//   id: string
//   name: string
// }

type TProps = {
  categories: TSupportCenterCategorySchema[]
  styles: TSectionStyleSchema
  heading: THeadingSchema
  pagination: TPaginationSchema
}

export const BSupportCenterSection = ({
  categories,
  styles,
  pagination,
}: TProps) => {
  const searchParams = useSearchParams()
  const router = useRouter()
  const pathname = usePathname()

  const isUpdatingFromUrl = useRef(false)

  const categoryIdFromUrl = searchParams.get('category')
  const searchQueryFromUrl = searchParams.get('search') || ''

  const initialCategory = categoryIdFromUrl
    ? categories.find((cat) => cat.id.toString() === categoryIdFromUrl) ||
      categories?.[0]
    : categories?.[0]

  const [currentCategory, setCurrentCategory] =
    useState<TSupportCenterCategorySchema | null>(initialCategory || null)
  const [searchQuery, setSearchQuery] = useState(searchQueryFromUrl)

  const locale = useLocale()

  // Update URL when state changes
  const updateUrl = (
    category: TSupportCenterCategorySchema | null,
    search: string,
  ) => {
    const params = new URLSearchParams()

    if (category?.id) {
      params.set('category', category.id.toString())
    }

    if (search.trim()) {
      params.set('search', search.trim())
    }

    const queryString = params.toString()
    const newUrl = queryString ? `${pathname}?${queryString}` : pathname

    isUpdatingFromUrl.current = true
    router.push(newUrl, { scroll: false })
  }

  const { data, isLoading, mutate } = useGetSupportCenters({
    filters: {
      support_center_category: {
        id: currentCategory?.id,
      },
    },
    locale,
    pagination: {
      page: pagination?.start || 1,
      pageSize: pagination?.limit || 10,
    },
    search: searchQuery,
  })

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    updateUrl(currentCategory, query)
  }

  // Handle category change - reset search
  const handleChangeCategory = (category: TSupportCenterCategorySchema) => {
    setCurrentCategory(category)
    setSearchQuery('')
    updateUrl(category, '')
  }

  // biome-ignore lint/correctness/useExhaustiveDependencies: URL sync logic needs specific dependencies
  useEffect(() => {
    const categoryIdFromUrl = searchParams.get('category')
    const searchQueryFromUrl = searchParams.get('search') || ''

    if (isUpdatingFromUrl.current) {
      isUpdatingFromUrl.current = false
      return
    }

    let hasChanges = false

    if (categoryIdFromUrl) {
      const categoryFromUrl = categories.find(
        (cat) => cat.id.toString() === categoryIdFromUrl,
      )
      if (categoryFromUrl && categoryFromUrl.id !== currentCategory?.id) {
        setCurrentCategory(categoryFromUrl)
        hasChanges = true
      }
    }

    if (searchQueryFromUrl !== searchQuery) {
      setSearchQuery(searchQueryFromUrl)
      hasChanges = true
    }

    if (hasChanges) {
      setTimeout(() => mutate(), 0)
    }
  }, [searchParams, categories, currentCategory?.id])

  // biome-ignore lint/correctness/useExhaustiveDependencies: mutate is stable from SWR
  useEffect(() => {
    mutate()
  }, [currentCategory?.id, searchQuery])

  // biome-ignore lint/correctness/useExhaustiveDependencies: Only run once on mount to set initial URL
  useEffect(() => {
    const categoryIdFromUrl = searchParams.get('category')

    if (!categoryIdFromUrl && currentCategory?.id) {
      updateUrl(currentCategory, searchQuery)
    }
  }, []) // Only run once on mount

  const ref = React.useRef(null)
  const isInView = useInView(ref, { once: true, margin: '-100px' })
  const accordionVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: (index: number) => ({
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, delay: index * 0.2, ease: 'easeOut' },
    }),
  }

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const renderCategories = useMemo(() => {
    return (
      <Card className="w-full max-w-sm 3xl:max-w-[428px] bg-slate-50 border-0 p-0 self-start">
        <CardContent className="p-0">
          <Typography
            variant="h6"
            className="font-bold p-4 2xl:p-6 2xl:pb-4 text-gray-800 border-b"
          >
            TOPICS
          </Typography>
          <nav>
            {categories?.map(
              (item: TSupportCenterCategorySchema, index: number) => (
                <Button
                  key={index}
                  variant="ghost"
                  className={cn(
                    'w-full justify-start rounded-none h-auto p-4 2xl:p-6 transition-colors text-sm md:text-base xl:text-[18px] font-medium',
                    currentCategory?.documentId === item.documentId
                      ? 'bg-yellow-400 text-slate-800 hover:bg-yellow-500'
                      : 'text-slate-600 hover:text-slate-800 hover:bg-slate-100',
                  )}
                  onClick={() => handleChangeCategory(item)}
                >
                  <Typography variant="body1">{item.name}</Typography>
                </Button>
              ),
            )}
          </nav>
        </CardContent>
      </Card>
    )
  }, [categories, currentCategory])

  return (
    <ContainerSection styles={styles || {}}>
      <div className="flex flex-col md:flex-row md:space-x-12 3xl:space-x-20 space-y-6 md:space-y-0">
        {renderCategories}

        <div className="flex-1" ref={ref}>
          <SearchBar
            onSearch={handleSearch}
            searchQuery={searchQuery}
            categoryType={currentCategory?.type || 'CONTENT'}
          />

          {isLoading ? (
            <SupportCenterContentSkeleton />
          ) : (
            <SupportContent
              categoryType={currentCategory?.type || 'CONTENT'}
              items={data?.data || []}
              accordionVariants={accordionVariants}
              isInView={isInView}
              searchQuery={searchQuery}
            />
          )}
        </div>
      </div>
    </ContainerSection>
  )
}

// #########################################################

type TSupportContentProps = {
  categoryType: 'CONTENT' | 'VIDEO' | 'DOCUMENT'
  items: TSupportCenterSchema[]
  accordionVariants: any
  isInView: boolean
  searchQuery: string
}

const SupportContent = ({
  categoryType,
  items,
  accordionVariants,
  isInView,
  searchQuery,
}: TSupportContentProps) => {
  const Comp = {
    CONTENT: SupportFAQTab,
    VIDEO: SupportVideoTab,
    DOCUMENT: SupportTechnicalTab,
  }

  const Component = Comp[categoryType as keyof typeof Comp] || SupportFAQTab

  return (
    <Component
      items={items}
      accordionVariants={accordionVariants}
      isInView={isInView}
      searchQuery={searchQuery}
    />
  )
}

// #########################################################

const SupportCenterContentSkeleton = () => {
  return (
    <div className="w-full space-y-4">
      <div className="grid grid-cols-1 lg:grid-cols-2 3xl:grid-cols-3 gap-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <Skeleton key={index} className="h-32 rounded-md" />
        ))}
      </div>

      <div className="space-y-3 mt-8">
        {Array.from({ length: 4 }).map((_, index) => (
          <Skeleton key={index} className="h-12 rounded-md" />
        ))}
      </div>
    </div>
  )
}

// #########################################################

// Separate Search Component - always visible
const SearchBar = ({
  onSearch,
  searchQuery,
  categoryType,
}: {
  onSearch: (query: string) => void
  searchQuery: string
  categoryType: 'CONTENT' | 'VIDEO' | 'DOCUMENT'
}) => {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)

  // Sync with parent
  useEffect(() => {
    setLocalSearchQuery(searchQuery)
  }, [searchQuery])

  const handleSearch = () => {
    const val = localSearchQuery.trim()
    onSearch(val)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setLocalSearchQuery(value)

    // Auto-search when input becomes empty
    if (!value.trim()) {
      onSearch('')
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') handleSearch()
  }

  // Different placeholders for different tab types
  const getPlaceholder = () => {
    switch (categoryType) {
      case 'VIDEO':
        return 'Tìm video hướng dẫn'
      case 'DOCUMENT':
        return 'Tìm tài liệu theo tên, model sản phẩm liên quan, danh mục sản phẩm, ngành công nghiệp...'
      default:
        return 'Tìm kiếm câu hỏi thường gặp'
    }
  }

  // Only show search for VIDEO and DOCUMENT tabs
  if (categoryType === 'CONTENT') {
    return null
  }

  return (
    <div className="relative flex-1 mb-6">
      <Input
        type="text"
        placeholder={getPlaceholder()}
        className="w-full rounded-md border shadow-sm border-gray-100 pl-9 pr-10 py-2 h-10 lg:h-13 text-sm sm:text-base"
        value={localSearchQuery}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
      />
      <SearchIcon
        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"
        size={16}
      />
      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
        <HexIconWrapper
          IconComponent={ArrowRight}
          sizeWrapper={36}
          size={16}
          onClick={handleSearch}
        />
      </div>
    </div>
  )
}

// #########################################################
