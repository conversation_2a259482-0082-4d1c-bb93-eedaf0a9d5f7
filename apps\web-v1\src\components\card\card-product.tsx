import { MainButton } from '@ttplatform/core-page-builder/components'
import { Checkbox, Typography } from '@ttplatform/ui/components'
import Image from 'next/image'
import Link from 'next/link'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'

//---------------------------------------------------------------------------------
type CardProps = {
  product: any
  specifications?: any[]
}
//---------------------------------------------------------------------------------
export function CardProduct({ product, specifications }: CardProps) {
  // Get specifications for this specific product from pre-fetched data
  const productSpecs = specifications?.filter(
    (spec: any) => spec.product_model?.documentId === product.documentId
  ) || []

  // Get top 3 most important specifications to display
  const displaySpecs = productSpecs.slice(0, 3)

  const productModelUrl = `/products/${product.slug || product.documentId}`

  return (
    <Link href={productModelUrl} className="block">
      <div
        className="relative flex flex-col bg-gray-50 rounded-lg overflow-hidden group hover:shadow-none cursor-pointer"
        style={{
          boxShadow:
            '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
        }}
      >
        <div className="p-4 absolute z-30 top-0 left-0">
          <div
            className="p-2 w-[36px] flex items-center gap-2 bg-white rounded-sm group-hover:w-[95px] transition-all duration-500 overflow-hidden"
            style={{
              boxShadow:
                '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
            }}
          >
            <Checkbox className="data-[state=checked]:text-black w-5 h-5 min-w-5 min-h-5" />
            <Typography
              variant={'caption'}
              className="text-gray-800 text-nowrap font-medium"
            >
              So sánh
            </Typography>
          </div>
        </div>
        <div className="relative z-10 w-full h-auto aspect-[4/3] flex items-end overflow-hidden ">
          <Image
            src={RenderImageUrlStrapi({ url: product?.image?.url })}
            alt={product?.image?.alternativeText || product?.name || 'Product image'}
            fill
            className="absolute z-20 w-full h-full object-cover group-hover:scale-110 ease-in-out transition-all duration-500"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          <div
            className="absolute bottom-0 left-0 z-50 w-full h-auto py-1 px-4 3xl:px-6 3xl:py-4"
            style={{
              background:
                'linear-gradient(90deg, rgba(255, 204, 0, 0.95) 70%, rgba(255, 204, 0, 0.85) 90%, rgba(255, 204, 0, 0.65) 100%)',
            }}
          >
            <div className="flex items-center justify-between">
              <Typography
                variant={'h6'}
                className="text-gray-800 uppercase line-clamp-1 font-semibold"
              >
                {product?.name}
              </Typography>
              <MainButton variant="secondary" />
            </div>
            {/* <div className="flex h-[52px] w-full">
            <div className="w-full px-6 h-full bg-[#FFCC00]/95 flex items-center">
              
            </div>
            <div className="h-full w-[120px] bg-[#FFCC00]/85"></div>
            <Link href={`/product/${product?.slug}`}>
              <div className="w-auto h-full px-2 sm:px-4 md:px-6 flex items-center  bg-[#FFCC00]/65">
               
              </div>
            </Link>
          </div> */}
          </div>
        </div>
        <div className="py-2 px-4 3xl:px-6 3xl:py-4 flex flex-col gap-4">
          {displaySpecs.length > 0 ? (
            displaySpecs.map((spec, index) => (
              <ItemDetails
                key={index}
                title={spec.product_specification.name}
                description={`${spec.value} ${spec.product_specification.unit || ''}`}
              />
            ))
          ) : (
            // Fallback to hardcoded values if no specifications available
            <>
              <ItemDetails
                title="Công suất hiệu dụng"
                description={product?.cong_suat || 'N/A'}
              />
              <ItemDetails
                title="Dung tích gầu"
                description={product?.dung_tich_gau || 'N/A'}
              />
              <ItemDetails
                title="Khối lượng vận hành"
                description={product?.khoi_luong_van_hanh || 'N/A'}
              />
            </>
          )}
        </div>
      </div>
    </Link>
  )
}

type ItemDetailsProps = {
  title: string
  description: string
}

function ItemDetails({ title, description }: ItemDetailsProps) {
  return (
    <div className="flex items-center justify-between">
      <Typography
        variant={'body2'}
        className="text-gray-800 font-bold line-clamp-2"
      >
        {title}
      </Typography>
      <Typography variant={'body2'} className="text-gray-800">
        {description}
      </Typography>
    </div>
  )
}
