'use client'

import { ContainerSection } from '@ttplatform/core-page-builder/components'
import { EmptyContent } from '@ttplatform/ui/templates'
import React, { useEffect, useState } from 'react'
import { LINGUI_CONFIG } from '~/src/config-global'
import { useGetArticles } from '~/src/libs/cms/strapi/use-articles'
import { SectionSkeleton } from '../../ui/skeleton'
import { LatestNews } from '../news-promotions'

interface NewsSectionProps {
  heading?: any
  locale?: string
  limit?: number
  skip?: number
  [key: string]: any
}

/**
 * News Section component with CMS data fetching
 */
const NewsSection: React.FC<NewsSectionProps> = ({
  heading,
  locale = LINGUI_CONFIG.defaultLocale,

  limit = 3,
  skip = 0,
  ...props
}) => {
  const [news, setNews] = useState<any[]>([])
  const { data, isLoading, error } = useGetArticles({
    locale,

    pagination: {
      limit: 3,
      start: 0,
    },
    sort: 'publishedAt:desc',
  })

  useEffect(() => {
    if (data?.data) {
      setNews(data?.data)
    }
  }, [data])

  const notFound = !isLoading && (error || !news?.length)

  if (isLoading) {
    return (
      <ContainerSection styles={{}}>
        <SectionSkeleton items={limit} showHeading={!!heading} />
      </ContainerSection>
    )
  }

  if (notFound) {
    return <EmptyContent title="Không tìm thấy dữ liệu" />
  }

  return <LatestNews news={news} heading={heading} locale={locale} {...props} />
}

export default NewsSection
