{"kind": "collectionType", "collectionName": "product_types", "info": {"singularName": "product-type", "pluralName": "product-types", "displayName": "Product Type"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string"}, "name": {"type": "string", "required": true}, "description": {"type": "text"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "product_types"}}}