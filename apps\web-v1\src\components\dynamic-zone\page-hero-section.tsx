'use client'

import {
  CmsMedia,
  LayoutContainer,
  MainButton,
} from '@ttplatform/core-page-builder/components'
import { THeadingSchema } from '@ttplatform/core-page-builder/libs'
import { Typography } from '@ttplatform/ui/components'
import StylesSection from '../renderer/styles-section'

//---------------------------------------------------------------------------------

type TProps = {
  styles?: any
  heading: THeadingSchema
  image?: any
}

export default function PageHeroSection({ styles, heading, image }: TProps) {
  const {
    heading: headingData,
    sub_heading: subHeadingData,
    buttons: buttonsData,
  } = heading || {}

  const renderHeading = (
    <Typography variant={'h1'} className="relative max-w-screen-md">
      {headingData?.text}
      <span className="block mx-auto w-[68px] h-[8px] rounded-none  bg-gradient-to-r from-[#FFCC00] via-[#FFD326] to-[#FFDE59]" />
    </Typography>
  )

  const renderSubHeading = (
    <Typography variant={'body1'} className="max-w-screen-lg">
      {subHeadingData?.text}
    </Typography>
  )

  const renderButtons = (
    <div className="flex justify-center gap-x-6 gap-y-4 flex-wrap ">
      {buttonsData?.map((button: any, idx: number) => (
        <MainButton
          key={idx}
          variant="secondary"
          isDisabledIcon={true}
          label={button?.text}
          url={button?.url}
          openInNewTab={button?.open_in_new_tab}
          icon={button?.icon}
        />
      ))}
    </div>
  )

  const renderContent = (
    <div className="w-full flex flex-col gap-4 md:gap-10">
      <div className="w-full max-w-screen-lg mx-auto flex flex-col gap-4 items-center text-center">
        {headingData?.text && renderHeading}
        {subHeadingData?.text && renderSubHeading}

        {buttonsData?.length ? renderButtons : null}
      </div>

      {image?.image?.url ? (
        <div className="w-full h-full">
          <CmsMedia
            media={image?.image}
            width={0}
            height={0}
            sizes="100vw"
            className="w-full h-full object-cover rounded-2xl"
          />
        </div>
      ) : null}
    </div>
  )

  if (styles) {
    return <StylesSection styles={styles || {}}>{renderContent}</StylesSection>
  }

  return <LayoutContainer>{renderContent}</LayoutContainer>
}
