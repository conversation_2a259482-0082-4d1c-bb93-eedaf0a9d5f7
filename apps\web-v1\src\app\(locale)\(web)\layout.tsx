import '@/styles/web.css'

import {
  BFooterWithSchema,
  BHeaderWithSchema,
  ScrollToTopButton,
} from '@ttplatform/core-page-builder/components'
import React from 'react'
import SearchBarComponent from '~/src/components/common/search-bar'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute, cmsContentType } from '~/src/libs/constants/cms'
import LanguageSwitcher from '../../../components/common/language-switcher'

import { getLocale } from '~/src/libs/data/cookies'
import { dynamicActivate } from '~/src/localization/i18n-dynamic'
// import ServerSiteHandler from '../server-site-handler'

type TProps = {
  children: React.ReactNode
}

export default async function Layout({ children }: TProps) {
  const locale = await getLocale()
  await dynamicActivate(locale)

  const navigationData = await fetchContentType({
    contentType: `${apiRoute.navigationRender}/${cmsContentType.mainNavigation}`,
    params: {
      locale,
      type: 'TREE',
    },
  })

  const topbarData = await fetchContentType({
    contentType: `${apiRoute.navigationRender}/${cmsContentType.topbarNavigation}`,
    params: {
      locale,
      type: 'TREE',
    },
  })

  const globalData = await fetchContentType({
    contentType: apiRoute.global,
    params: {
      locale,
    },
  })
  const { header, footer } = globalData?.data || {}

  return (
    <>
      <BHeaderWithSchema
        header={header}
        navigation={navigationData}
        locale={locale}
        topbar={topbarData}
        renderSearchBar={<SearchBarComponent />}
        renderLanguageSwitcher={<LanguageSwitcher currentLocale={locale} />}
      />

      {children}

      <BFooterWithSchema data={footer} />

      <ScrollToTopButton />

      {/* <ServerSiteHandler /> */}
    </>
  )
}
