import ClientSlugHandler from '~/src/app/(locale)/client-slug-handler'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import { PageBreadcrumb } from '~/src/modules/layout'

//----------------------------------------------------------------------------------
type TProps = {
  slug: string
}
//----------------------------------------------------------------------------------
export default async function SolutionCategoryView({ slug }: TProps) {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.industrialSolutionCategories,
    params: {
      filters: {
        slug,
        locale,
      },
    },
    spreadData: true,
  })

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = localization.slug

      return acc
    },
    { [locale]: slug },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />

      <PageBreadcrumb items={[{ title: pageData?.name }]} />
      {/* <ServerSiteHandler /> */}
      <PageContent pageData={pageData} />
    </>
  )
}
