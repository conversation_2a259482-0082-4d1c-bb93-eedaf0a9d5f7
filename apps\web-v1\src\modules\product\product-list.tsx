'use client'

import { useCallback, useState } from 'react'
import StylesSection from '~/src/components/renderer/styles-section'
import FilterBar from './components/filter-bar'
import FilterResults from './components/filter-results'
//---------------------------------------------------------------------------------
//---------------------------------------------------------------------------------
type ProductListProps = {
  productSlug?: string
}

export default function ProductList({ productSlug }: ProductListProps) {
  const [filters, setFilters] = useState<any>({ displayMode: 'grid' })

  const handleFilters = useCallback((name: string, value: any) => {
    setFilters((prevState: any) => ({
      ...prevState,
      [name]: value,
    }))
  }, [])

  return (
    <StylesSection styles={{}}>
      <div className="relative z-30 w-full">
        <div className="grid grid-cols-12 gap-x-5 md:gap-x-10">
          <div className="col-span-12 md:col-span-3">
            <FilterBar
              filters={filters}
              onFilters={handleFilters}
              productSlug={productSlug}
            />
          </div>
          <div className="col-span-12 md:col-span-9">
            <FilterResults
              filters={filters}
              onFilters={handleFilters}
              productSlug={productSlug}
            />
          </div>
        </div>
      </div>
    </StylesSection>
  )
}
