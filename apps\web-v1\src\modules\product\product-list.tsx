'use client'

import { useCallback, useState } from 'react'
import StylesSection from '~/src/components/renderer/styles-section'
import FilterBar from './components/filter-bar'
import FilterResults from './components/filter-results'
//---------------------------------------------------------------------------------
const defaultFilters: any = {
  category: 'may-xuc-dao-mini',
  boltLength: [50, 100],
  boreDiameter: '134,5',
  efficiencyRating: '200-micron',
  insideDiameter: '6.0',
  displayMode: 'grid',
}
//---------------------------------------------------------------------------------
export default function ProductList() {
  const [filters, setFilters] = useState(defaultFilters)

  const handleFilters = useCallback((name: string, value: any) => {
    setFilters((prevState: any) => ({
      ...prevState,
      [name]: value,
    }))
  }, [])

  return (
    <StylesSection styles={{}}>
      <div className="relative z-30 w-full">
        <div className="grid grid-cols-12 gap-x-5 md:gap-x-10">
          <div className="col-span-12 md:col-span-3">
            <FilterBar filters={filters} onFilters={handleFilters} />
          </div>
          <div className="col-span-12 md:col-span-9">
            <FilterResults filters={filters} onFilters={handleFilters} />
          </div>
        </div>
      </div>
    </StylesSection>
  )
}
