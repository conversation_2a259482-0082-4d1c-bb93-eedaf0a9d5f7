export const _global = {
  id: 23,
  documentId: 'en793kiwci00j3coe6fxod3r',
  createdAt: '2025-06-15T18:40:31.163Z',
  updatedAt: '2025-06-16T06:02:21.692Z',
  publishedAt: '2025-06-16T06:02:21.793Z',
  locale: 'vi',
  header: {},
  footer: {
    id: 22,
    description:
      'Phú Thái Cat tự hào là Đại lý <PERSON> thức <PERSON> nhất của Caterpillar tại Việt Nam. Chúng tôi cam kết mang đến cho khách hàng những sản phẩm ch<PERSON>h hãng, đ<PERSON><PERSON><PERSON> b<PERSON><PERSON> hành toàn cầu với chất lượng vượt trội và công nghệ tiên tiến nhất hiện nay cùng dịch vụ hỗ trợ sản phẩm hàng đầu.\n\n',
    copyright: null,
    built_with: null,
    logo: {
      id: 65,
      documentId: 'dz3ben2gcbc3eas1rd7lrmrr',
      company: 'PTC Logo',
      createdAt: '2025-06-15T18:42:45.857Z',
      updatedAt: '2025-06-15T18:42:45.857Z',
      publishedAt: '2025-06-15T18:42:45.850Z',
      image: {
        id: 179,
        documentId: 'hxz1wxf2l9gp6tkokh8dhb9r',
        name: 'ptc.png',
        alternativeText: null,
        caption: null,
        width: 1000,
        height: 250,
        formats: {
          small: {
            ext: '.png',
            url: '/uploads/small_ptc_f779d13960.png',
            hash: 'small_ptc_f779d13960',
            mime: 'image/png',
            name: 'small_ptc.png',
            path: null,
            size: 10.27,
            width: 500,
            height: 125,
            sizeInBytes: 10267,
          },
          medium: {
            ext: '.png',
            url: '/uploads/medium_ptc_f779d13960.png',
            hash: 'medium_ptc_f779d13960',
            mime: 'image/png',
            name: 'medium_ptc.png',
            path: null,
            size: 17.68,
            width: 750,
            height: 188,
            sizeInBytes: 17675,
          },
          thumbnail: {
            ext: '.png',
            url: '/uploads/thumbnail_ptc_f779d13960.png',
            hash: 'thumbnail_ptc_f779d13960',
            mime: 'image/png',
            name: 'thumbnail_ptc.png',
            path: null,
            size: 5.44,
            width: 245,
            height: 61,
            sizeInBytes: 5438,
          },
        },
        hash: 'ptc_f779d13960',
        ext: '.png',
        mime: 'image/png',
        size: 5.54,
        url: '/uploads/ptc_f779d13960.png',
        previewUrl: null,
        provider: 'local',
        provider_metadata: null,
        createdAt: '2025-06-15T18:42:11.126Z',
        updatedAt: '2025-06-15T18:42:11.126Z',
        publishedAt: '2025-06-15T18:42:11.126Z',
      },
    },
    policy_links: [],
    social_media_links: [
      {
        id: 295,
        text: 'Facebook',
        url: 'https://www.facebook.com',
        target: '_blank',
        icon: {
          id: 183,
          documentId: 'bwg6qoug73j98cv7tayhec1w',
          name: 'ic-facebook.png',
          alternativeText: null,
          caption: null,
          width: 512,
          height: 512,
          formats: {
            small: {
              ext: '.png',
              url: '/uploads/small_ic_facebook_8f06b55785.png',
              hash: 'small_ic_facebook_8f06b55785',
              mime: 'image/png',
              name: 'small_ic-facebook.png',
              path: null,
              size: 23.24,
              width: 500,
              height: 500,
              sizeInBytes: 23240,
            },
            thumbnail: {
              ext: '.png',
              url: '/uploads/thumbnail_ic_facebook_8f06b55785.png',
              hash: 'thumbnail_ic_facebook_8f06b55785',
              mime: 'image/png',
              name: 'thumbnail_ic-facebook.png',
              path: null,
              size: 5.98,
              width: 156,
              height: 156,
              sizeInBytes: 5976,
            },
          },
          hash: 'ic_facebook_8f06b55785',
          ext: '.png',
          mime: 'image/png',
          size: 4.02,
          url: '/uploads/ic_facebook_8f06b55785.png',
          previewUrl: null,
          provider: 'local',
          provider_metadata: null,
          createdAt: '2025-06-16T06:00:48.802Z',
          updatedAt: '2025-06-16T06:00:48.802Z',
          publishedAt: '2025-06-16T06:00:48.802Z',
          related: [
            {
              __type: 'shared.link',
              id: 269,
              text: 'Facebook',
              url: 'https://www.facebook.com',
              target: '_blank',
            },
            {
              __type: 'shared.link',
              id: 295,
              text: 'Facebook',
              url: 'https://www.facebook.com',
              target: '_blank',
            },
          ],
        },
      },
      {
        id: 296,
        text: 'Youtube',
        url: 'https://www.youtube.com/',
        target: '_blank',
        icon: {
          id: 187,
          documentId: 'y9yabosgg3m8hxh7hkyuvuep',
          name: 'ic-youtube.png',
          alternativeText: null,
          caption: null,
          width: 512,
          height: 512,
          formats: {
            small: {
              ext: '.png',
              url: '/uploads/small_ic_youtube_29b7240592.png',
              hash: 'small_ic_youtube_29b7240592',
              mime: 'image/png',
              name: 'small_ic-youtube.png',
              path: null,
              size: 17.34,
              width: 500,
              height: 500,
              sizeInBytes: 17341,
            },
            thumbnail: {
              ext: '.png',
              url: '/uploads/thumbnail_ic_youtube_29b7240592.png',
              hash: 'thumbnail_ic_youtube_29b7240592',
              mime: 'image/png',
              name: 'thumbnail_ic-youtube.png',
              path: null,
              size: 4.05,
              width: 156,
              height: 156,
              sizeInBytes: 4053,
            },
          },
          hash: 'ic_youtube_29b7240592',
          ext: '.png',
          mime: 'image/png',
          size: 3.82,
          url: '/uploads/ic_youtube_29b7240592.png',
          previewUrl: null,
          provider: 'local',
          provider_metadata: null,
          createdAt: '2025-06-16T06:00:48.844Z',
          updatedAt: '2025-06-16T06:01:20.772Z',
          publishedAt: '2025-06-16T06:00:48.845Z',
          related: [
            {
              __type: 'shared.link',
              id: 296,
              text: 'Youtube',
              url: 'https://www.youtube.com/',
              target: '_blank',
            },
            {
              __type: 'shared.link',
              id: 270,
              text: 'Youtube',
              url: 'https://www.youtube.com/',
              target: '_blank',
            },
          ],
        },
      },
      {
        id: 297,
        text: 'LinkedIn',
        url: 'https://www.linkedin.com/',
        target: '_blank',
        icon: {
          id: 184,
          documentId: 'w4in7cpl8w344pdl4abqn2y8',
          name: 'ic-linkedin.png',
          alternativeText: null,
          caption: null,
          width: 512,
          height: 512,
          formats: {
            small: {
              ext: '.png',
              url: '/uploads/small_ic_linkedin_2a63673c40.png',
              hash: 'small_ic_linkedin_2a63673c40',
              mime: 'image/png',
              name: 'small_ic-linkedin.png',
              path: null,
              size: 25.82,
              width: 500,
              height: 500,
              sizeInBytes: 25819,
            },
            thumbnail: {
              ext: '.png',
              url: '/uploads/thumbnail_ic_linkedin_2a63673c40.png',
              hash: 'thumbnail_ic_linkedin_2a63673c40',
              mime: 'image/png',
              name: 'thumbnail_ic-linkedin.png',
              path: null,
              size: 6.78,
              width: 156,
              height: 156,
              sizeInBytes: 6779,
            },
          },
          hash: 'ic_linkedin_2a63673c40',
          ext: '.png',
          mime: 'image/png',
          size: 4.18,
          url: '/uploads/ic_linkedin_2a63673c40.png',
          previewUrl: null,
          provider: 'local',
          provider_metadata: null,
          createdAt: '2025-06-16T06:00:48.806Z',
          updatedAt: '2025-06-16T06:00:48.806Z',
          publishedAt: '2025-06-16T06:00:48.806Z',
          related: [
            {
              __type: 'shared.link',
              id: 293,
              text: 'LinkedIn',
              url: 'https://www.linkedin.com/',
              target: '_blank',
            },
            {
              __type: 'shared.link',
              id: 297,
              text: 'LinkedIn',
              url: 'https://www.linkedin.com/',
              target: '_blank',
            },
          ],
        },
      },
      {
        id: 298,
        text: 'Tiktok',
        url: 'https://www.tiktok.com/',
        target: '_blank',
        icon: {
          id: 186,
          documentId: 'k3z49a5njfkse3i8huy4xuw8',
          name: 'ic-tiktok.png',
          alternativeText: null,
          caption: null,
          width: 512,
          height: 512,
          formats: {
            small: {
              ext: '.png',
              url: '/uploads/small_ic_tiktok_2c376165e2.png',
              hash: 'small_ic_tiktok_2c376165e2',
              mime: 'image/png',
              name: 'small_ic-tiktok.png',
              path: null,
              size: 38.28,
              width: 500,
              height: 500,
              sizeInBytes: 38279,
            },
            thumbnail: {
              ext: '.png',
              url: '/uploads/thumbnail_ic_tiktok_2c376165e2.png',
              hash: 'thumbnail_ic_tiktok_2c376165e2',
              mime: 'image/png',
              name: 'thumbnail_ic-tiktok.png',
              path: null,
              size: 10.22,
              width: 156,
              height: 156,
              sizeInBytes: 10221,
            },
          },
          hash: 'ic_tiktok_2c376165e2',
          ext: '.png',
          mime: 'image/png',
          size: 7.2,
          url: '/uploads/ic_tiktok_2c376165e2.png',
          previewUrl: null,
          provider: 'local',
          provider_metadata: null,
          createdAt: '2025-06-16T06:00:48.836Z',
          updatedAt: '2025-06-16T06:00:48.836Z',
          publishedAt: '2025-06-16T06:00:48.836Z',
          related: [
            {
              __type: 'shared.link',
              id: 294,
              text: 'Tiktok',
              url: 'https://www.tiktok.com/',
              target: '_blank',
            },
            {
              __type: 'shared.link',
              id: 298,
              text: 'Tiktok',
              url: 'https://www.tiktok.com/',
              target: '_blank',
            },
          ],
        },
      },
    ],
    footer_nav: [
      {
        id: 4,
        title: 'TRUY CẬP NHANH',
        links: [
          {
            id: 299,
            text: 'Dấu mốc phát triển',
            url: '#',
            target: '_self',
            icon: null,
          },
          {
            id: 300,
            text: 'Về chúng tôi',
            url: '/gioi-thieu',
            target: '_self',
            icon: null,
          },
        ],
      },
    ],
    attached_images: [
      {
        id: 181,
        documentId: 'b6qvtt9lbe2ifktuxv5zipmx',
        name: 'bo-cong-thuong.png',
        alternativeText: null,
        caption: null,
        width: 163,
        height: 63,
        formats: null,
        hash: 'bo_cong_thuong_ecd94208c6',
        ext: '.png',
        mime: 'image/png',
        size: 2.48,
        url: '/uploads/bo_cong_thuong_ecd94208c6.png',
        previewUrl: null,
        provider: 'local',
        provider_metadata: null,
        createdAt: '2025-06-15T18:46:37.487Z',
        updatedAt: '2025-06-15T18:46:37.487Z',
        publishedAt: '2025-06-15T18:46:37.488Z',
        related: [
          {
            __type: 'global.footer',
            id: 18,
            description:
              'Phú Thái Cat tự hào là Đại lý Chính thức Duy nhất của Caterpillar tại Việt Nam. Chúng tôi cam kết mang đến cho khách hàng những sản phẩm chính hãng, được bảo hành toàn cầu với chất lượng vượt trội và công nghệ tiên tiến nhất hiện nay cùng dịch vụ hỗ trợ sản phẩm hàng đầu.\n\n',
            copyright: null,
            built_with: null,
          },
          {
            __type: 'global.footer',
            id: 22,
            description:
              'Phú Thái Cat tự hào là Đại lý Chính thức Duy nhất của Caterpillar tại Việt Nam. Chúng tôi cam kết mang đến cho khách hàng những sản phẩm chính hãng, được bảo hành toàn cầu với chất lượng vượt trội và công nghệ tiên tiến nhất hiện nay cùng dịch vụ hỗ trợ sản phẩm hàng đầu.\n\n',
            copyright: null,
            built_with: null,
          },
        ],
      },
      {
        id: 180,
        documentId: 'b4wz4n9a56hkz1jnhns7h7yo',
        name: 'iso-2018.png',
        alternativeText: null,
        caption: null,
        width: 65,
        height: 66,
        formats: null,
        hash: 'iso_2018_01d540eaca',
        ext: '.png',
        mime: 'image/png',
        size: 1.97,
        url: '/uploads/iso_2018_01d540eaca.png',
        previewUrl: null,
        provider: 'local',
        provider_metadata: null,
        createdAt: '2025-06-15T18:46:37.484Z',
        updatedAt: '2025-06-15T18:46:37.484Z',
        publishedAt: '2025-06-15T18:46:37.484Z',
        related: [
          {
            __type: 'global.footer',
            id: 18,
            description:
              'Phú Thái Cat tự hào là Đại lý Chính thức Duy nhất của Caterpillar tại Việt Nam. Chúng tôi cam kết mang đến cho khách hàng những sản phẩm chính hãng, được bảo hành toàn cầu với chất lượng vượt trội và công nghệ tiên tiến nhất hiện nay cùng dịch vụ hỗ trợ sản phẩm hàng đầu.\n\n',
            copyright: null,
            built_with: null,
          },
          {
            __type: 'global.footer',
            id: 22,
            description:
              'Phú Thái Cat tự hào là Đại lý Chính thức Duy nhất của Caterpillar tại Việt Nam. Chúng tôi cam kết mang đến cho khách hàng những sản phẩm chính hãng, được bảo hành toàn cầu với chất lượng vượt trội và công nghệ tiên tiến nhất hiện nay cùng dịch vụ hỗ trợ sản phẩm hàng đầu.\n\n',
            copyright: null,
            built_with: null,
          },
        ],
      },
      {
        id: 182,
        documentId: 'zw5b29hcli08ml7jitp3j6pq',
        name: 'iso-2015.png',
        alternativeText: null,
        caption: null,
        width: 65,
        height: 66,
        formats: null,
        hash: 'iso_2015_abc0deef22',
        ext: '.png',
        mime: 'image/png',
        size: 1.77,
        url: '/uploads/iso_2015_abc0deef22.png',
        previewUrl: null,
        provider: 'local',
        provider_metadata: null,
        createdAt: '2025-06-15T18:46:37.490Z',
        updatedAt: '2025-06-15T18:46:37.490Z',
        publishedAt: '2025-06-15T18:46:37.490Z',
        related: [
          {
            __type: 'global.footer',
            id: 18,
            description:
              'Phú Thái Cat tự hào là Đại lý Chính thức Duy nhất của Caterpillar tại Việt Nam. Chúng tôi cam kết mang đến cho khách hàng những sản phẩm chính hãng, được bảo hành toàn cầu với chất lượng vượt trội và công nghệ tiên tiến nhất hiện nay cùng dịch vụ hỗ trợ sản phẩm hàng đầu.\n\n',
            copyright: null,
            built_with: null,
          },
          {
            __type: 'global.footer',
            id: 22,
            description:
              'Phú Thái Cat tự hào là Đại lý Chính thức Duy nhất của Caterpillar tại Việt Nam. Chúng tôi cam kết mang đến cho khách hàng những sản phẩm chính hãng, được bảo hành toàn cầu với chất lượng vượt trội và công nghệ tiên tiến nhất hiện nay cùng dịch vụ hỗ trợ sản phẩm hàng đầu.\n\n',
            copyright: null,
            built_with: null,
          },
        ],
      },
    ],
    footer_contact: {
      id: 4,
      title: 'THÔNG TIN LIÊN HỆ',
      items: [
        {
          id: 14,
          title: 'Địa chỉ:',
          description:
            ' Tầng 14 & 16, Tòa nhà Plaschem, Số 562, Nguyễn Văn Cừ, Quận Long Biên, Hà Nội',
        },
        {
          id: 15,
          title: 'Hotline:',
          description: '1800 599 990 - VP Hà Nội: 024 3652 6999',
        },
        {
          id: 16,
          title: 'Email:',
          description: '<EMAIL>',
        },
        {
          id: 17,
          title: 'Website: ',
          description: 'www.phuthaicat.com.vn',
        },
      ],
    },
  },
  navbar: null,
  seo: {
    id: 109,
    metaTitle: 'Phú Thái Cat - Kiến tạo để Dẫn đầu',
    metaDescription:
      'Phú Thái Cat là minh chứng cho sự phát triển bền vững và không ngừng đổi mới. Từ những bước đi đầu tiên đến vị thế vững chắc hôm nay, chúng tôi luôn giữ vững cam kết hợp tác dài hạn và mang đến những giá trị tốt nhất cho khách hàng, đối tác và cộng đồng.',
    keywords: null,
    metaRobots: null,
    structuredData: null,
    metaViewport: null,
    canonicalURL: null,
    metaImage: null,
  },
  localizations: [
    {
      id: 21,
      documentId: 'en793kiwci00j3coe6fxod3r',
      createdAt: '2024-08-07T10:40:21.837Z',
      updatedAt: '2025-06-15T19:14:36.229Z',
      publishedAt: '2025-06-15T19:14:36.320Z',
      locale: 'en',
    },
  ],
}
