'use client'

import { Icon } from '@iconify/react'
import {
  BgCommitment,
  MainButton,
  SectionTitle,
} from '@ttplatform/core-page-builder/components'
import {
  TButtonStylePreset,
  getTextClass,
} from '@ttplatform/core-page-builder/libs'
import { Marquee, Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import gsap from 'gsap'
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger'
import Image from 'next/image'
import { useEffect, useRef } from 'react'
import { useMediaQuery } from 'usehooks-ts'
import HeadingSection from '../../renderer/heading-section'
import StylesSection from '../../renderer/styles-section'

gsap.registerPlugin(ScrollTrigger)

// Mock data for testing
const mockHeading = {
  type: 'default',
  heading: {
    text: 'ĐỐI TÁC CỦA CÁC THƯƠNG HIỆU HÀNG ĐẦU THẾ GIỚI',
    styles: {
      color: '#182230',
      text_align: 'text-center',
    },
  },
  sub_heading: null,
  buttons: [],
}

const mockPartners = [
  {
    logo: {
      url: '/icons/partner/partner-logo-1.png',
      alt: 'Logoipsum 1',
      active: true,
      imageStyle: 'medium',
    },
    name: 'Partner 1',
    rank: '1',
  },
  {
    logo: {
      url: '/icons/partner/partner-logo-2.png',
      alt: 'Logoipsum 2',
      active: true,
      imageStyle: 'medium',
    },
    name: 'Partner 2',
    rank: '2',
  },
  {
    logo: {
      url: '/icons/partner/partner-logo-3.png',
      alt: 'Logoipsum 3',
      active: true,
      imageStyle: 'medium',
    },
    name: 'Partner 3',
    rank: '3',
  },
  {
    logo: {
      url: '/icons/partner/partner-logo-1.png',
      alt: 'Logoipsum 4',
      active: true,
      imageStyle: 'medium',
    },
    name: 'Partner 4',
    rank: '4',
  },
  {
    logo: {
      url: '/icons/partner/partner-logo-2.png',
      alt: 'Logoipsum 5',
      active: true,
      imageStyle: 'medium',
    },
    name: 'Partner 5',
    rank: '5',
  },
]

const mockCommitment = {
  title: {
    text: 'CAM KẾT TỪ PHÚ THÁI CAT',
    active: true,
    textStyle: 'heading2',
  },
  items: [
    {
      text: 'Có mặt trong vòng 24h',
      active: true,
    },
    {
      text: 'Đưa máy hoạt động trở lại trong vòng 48h',
      active: true,
    },
  ],
  icon: {
    styles: {
      background: { color: '#FFC107' },
      border: { radius: '9999px' },
      width: '28',
      height: '28',
    },
  },
  background: {
    url: '/icons/cva.png',
    active: true,
  },
  button: {
    label: 'GET STARTED',
    url: '#',
    active: true,
    variant: 'secondary',
    open_in_new_tab: false,
  },
}

type TProps = {
  heading?: any
  styles?: any
  partners: any
  title?: any
  commitment?: any
  palette?: any
}

const PartnerCommitmentSection: React.FC<TProps> = ({
  styles,
  heading = mockHeading,
  partners = mockPartners,
  commitment = mockCommitment,
}) => {
  const containerRef = useRef(null)
  const partnersBannerRef = useRef(null)
  const commitmentSectionRef = useRef(null)
  const isDesktop = useMediaQuery('(min-width: 1280px)') // Only on large desktop screens

  useEffect(() => {
    if (typeof window === 'undefined') return

    // Disable parallax on mobile/tablet completely for SEO and performance
    if (!isDesktop) {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
      return
    }

    const container = containerRef.current
    const partnersBanner = partnersBannerRef.current
    const commitmentSection = commitmentSectionRef.current

    if (!container) return

    if (partnersBanner) {
      gsap.fromTo(
        partnersBanner,
        { y: 80 },
        {
          y: -80,
          ease: 'none',
          scrollTrigger: {
            trigger: container,
            start: 'top bottom',
            end: 'bottom top',
            scrub: 0.5,
            invalidateOnRefresh: true,
          },
        },
      )
    }

    if (commitmentSection) {
      gsap.fromTo(
        commitmentSection,
        { y: -80 },
        {
          y: 80,
          ease: 'none',
          scrollTrigger: {
            trigger: container,
            start: 'top bottom',
            end: 'bottom top',
            scrub: 0.5,
            invalidateOnRefresh: true,
          },
        },
      )
    }

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
    }
  }, [isDesktop])

  return (
    <StylesSection styles={styles}>
      <div ref={containerRef}>
        {/* Partners banner */}
        <div
          ref={partnersBannerRef}
          className="relative mx-auto w-full py-8 xl:h-[120vh] 3xl:min-h-screen lg:py-12 xl:pb-40"
          style={isDesktop ? { willChange: 'transform' } : {}}
        >
          <div
            className="rounded-lg py-6 sm:px-8 w-full flex flex-col gap-4 sm:gap-10"
            style={{
              backgroundImage:
                'linear-gradient(270deg, rgb(255, 222, 89) 0%, rgb(255, 211, 38) 16%, rgb(255, 204, 0) 42%)',
            }}
          >
            <div className="text-center">
              <HeadingSection
                heading={heading}
                buttonsClassName="justify-center"
                underlineBackground="#182230"
              />
            </div>
            <div className="flex items-center justify-between relative">
              <Marquee speed={10}>
                {partners?.length > 0 &&
                  partners.map((partner: any, index: number) =>
                    partner.logo.active ? (
                      <Image
                        key={index}
                        src={partner.logo.url}
                        alt={partner.logo.alt}
                        width={500}
                        height={500}
                        className="h-8 sm:h-10 w-auto object-cover transition-transform duration-300 hover:scale-105"
                      />
                    ) : null,
                  )}
              </Marquee>
            </div>
          </div>
        </div>

        {/* Commitment section */}
        {commitment && commitment.background.active && (
          <div
            ref={commitmentSectionRef}
            className="mt-8 xl:mt-0 xl:absolute z-2 max-w-[720px] xl:right-10 xl:bottom-20 ml-auto"
            style={isDesktop ? { willChange: 'transform' } : {}}
          >
            <div className="relative rounded-xl w-full">
              <div className="h-auto w-1/5 cursor-pointer absolute -top-10 xs:-top-12 sm:-top-16 right-0 -translate-x-1/2 flex items-center justify-center z-10 hover:contrast-125">
                <Image
                  src="/icons/cva.png"
                  alt="cva"
                  width={100}
                  height={100}
                  priority
                  className="h-full w-full aspect-square object-cover "
                />
              </div>
              <div className="opacity-80">
                <BgCommitment className="w-full h-full" />
                <div className="w-full bg-[#E3E3E3] h-40 xs:h-20 sm:h-0 -translate-y-6 rounded-bl-xl rounded-br-xl"></div>
              </div>

              <div className="absolute z-10 top-1/2 left-5 sm:left-8 -translate-y-1/2">
                {commitment.title.active && (
                  <SectionTitle
                    text={commitment.title.text}
                    className="text-gray-900 3xl:text-4xl"
                    underlineColor="bg-slate-900"
                  />
                )}
                <ul className="space-y-4 my-6">
                  {commitment.items.map((item: any, index: number) =>
                    item.active ? (
                      <li key={index} className="flex items-center gap-3">
                        <div className="h-6 w-6 flex items-center justify-center bg-yellow-400 rounded-full">
                          <Icon
                            icon="material-symbols:check-rounded"
                            width="18"
                            height="18"
                            className="text-gray-700"
                          />
                        </div>
                        <Typography
                          variant="body2"
                          classNames={{
                            root: cn(
                              getTextClass('body'),
                              'text-base md:text-lg font-normal text-gray-900',
                            ),
                          }}
                        >
                          {item.text}
                        </Typography>
                      </li>
                    ) : null,
                  )}
                </ul>
                {commitment.button.active && (
                  <MainButton
                    label={commitment.button.label}
                    variant={
                      (commitment.button.variant ||
                        'primary') as TButtonStylePreset
                    }
                  />
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </StylesSection>
  )
}

export default PartnerCommitmentSection
